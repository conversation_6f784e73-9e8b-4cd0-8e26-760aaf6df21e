const fs = require('fs').promises;
const path = require('path');
const sqlite3 = require('sqlite3').verbose();
const { promisify } = require('util');
const { initializePlugin, destroyPlugin } = require('./ServerIntegration');
const EmotionalDataConnector = require('./EmotionalDataConnector');
const TimeArchitectureManager = require('./TimeArchitectureManager');

/**
 * 世界树角色管理系统核心类
 * 管理角色的时间架构、日程表和心理活动
 */
class WorldTreeCore {
    constructor(config, logger) {
        this.config = config;
        this.logger = logger;
        this.db = null;
        this.isInitialized = false;
        
        // 配置参数
        this.openaiConfig = {
            apiKey: config.OPENAI_API_KEY,
            baseURL: config.OPENAI_BASE_URL || 'https://api.openai.com/v1',
            model: config.OPENAI_MODEL || 'gpt-3.5-turbo'
        };
        
        this.psychologyEnabled = config.PSYCHOLOGY_ANALYSIS_ENABLED !== 'false';
        this.scheduleInterval = parseInt(config.SCHEDULE_UPDATE_INTERVAL) || 30;
        this.debugMode = config.DEBUG_MODE === 'true';
        
        // 内存缓存
        this.agentCache = new Map();
        this.scheduleCache = new Map();
        this.psychologyCache = new Map();

        // 定时器
        this.scheduleTimer = null;

        // 子系统
        this.emotionalConnector = new EmotionalDataConnector(logger);
        this.timeManager = new TimeArchitectureManager(logger);
        
        this.logger.info('世界树系统', '初始化世界树角色管理系统');
    }

    /**
     * 初始化插件
     */
    async initialize() {
        try {
            await this.initializeDatabase();
            await this.loadAgentList();
            await this.emotionalConnector.connect();
            this.startScheduleUpdater();

            this.isInitialized = true;
            this.logger.success('世界树系统', '✅ 世界树系统初始化完成');

        } catch (error) {
            this.logger.error('世界树系统', '初始化失败:', error);
            throw error;
        }
    }

    /**
     * 初始化数据库
     */
    async initializeDatabase() {
        const dbPath = path.join(__dirname, 'worldtree.db');
        
        return new Promise((resolve, reject) => {
            this.db = new sqlite3.Database(dbPath, (err) => {
                if (err) {
                    reject(err);
                    return;
                }
                
                this.dbRun = promisify(this.db.run.bind(this.db));
                this.dbGet = promisify(this.db.get.bind(this.db));
                this.dbAll = promisify(this.db.all.bind(this.db));
                
                this.createTables().then(resolve).catch(reject);
            });
        });
    }

    /**
     * 创建数据库表
     */
    async createTables() {
        const tables = [
            // 角色配置表
            `CREATE TABLE IF NOT EXISTS agent_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                agent_name TEXT NOT NULL UNIQUE,
                display_name TEXT,
                time_architecture TEXT, -- JSON格式的时间架构
                schedule_data TEXT, -- JSON格式的日程表
                personality_traits TEXT, -- JSON格式的性格特征
                background_story TEXT, -- 背景故事
                current_mood TEXT DEFAULT 'neutral', -- 当前心情
                stress_level REAL DEFAULT 0.0, -- 压力等级
                energy_level REAL DEFAULT 1.0, -- 精力等级
                last_updated TEXT NOT NULL, -- 最后更新时间（本地时间）
                created_at TEXT NOT NULL -- 创建时间（本地时间）
            )`,
            
            // 心理活动记录表（支持多用户分层）
            `CREATE TABLE IF NOT EXISTS psychology_activities (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                agent_name TEXT NOT NULL,
                activity_type TEXT NOT NULL, -- 'thought', 'emotion', 'reflection'
                content TEXT NOT NULL, -- 心理活动内容
                trigger_context TEXT, -- 触发上下文
                emotional_state TEXT, -- JSON格式的情绪状态
                confidence_score REAL DEFAULT 0.5,
                timestamp TEXT NOT NULL, -- 本地时间格式
                session_id TEXT, -- 会话ID，用于关联对话
                interaction_count INTEGER DEFAULT 0, -- 交互次数
                FOREIGN KEY (agent_name) REFERENCES agent_configs(agent_name)
            )`,

            // 用户-角色关系表（分层架构）
            `CREATE TABLE IF NOT EXISTS user_agent_relationships (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                agent_name TEXT NOT NULL,
                relationship_level INTEGER DEFAULT 1, -- 关系等级 1-10
                interaction_count INTEGER DEFAULT 0,
                total_conversation_time INTEGER DEFAULT 0, -- 总对话时间（分钟）
                last_interaction TEXT, -- 最后交互时间
                relationship_notes TEXT, -- 关系备注
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                UNIQUE(user_id, agent_name),
                FOREIGN KEY (agent_name) REFERENCES agent_configs(agent_name)
            )`,

            // 角色状态快照表（用于状态恢复）
            `CREATE TABLE IF NOT EXISTS agent_state_snapshots (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id TEXT NOT NULL,
                agent_name TEXT NOT NULL,
                snapshot_type TEXT NOT NULL, -- 'daily', 'weekly', 'manual'
                state_data TEXT NOT NULL, -- JSON格式的状态数据
                mood_state TEXT,
                stress_level REAL,
                energy_level REAL,
                context_summary TEXT, -- 上下文摘要
                created_at TEXT NOT NULL,
                FOREIGN KEY (agent_name) REFERENCES agent_configs(agent_name)
            )`,
            
            // 日程事件表
            `CREATE TABLE IF NOT EXISTS schedule_events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                agent_name TEXT NOT NULL,
                event_title TEXT NOT NULL,
                event_description TEXT,
                start_time TEXT NOT NULL, -- 本地时间格式
                end_time TEXT,
                event_type TEXT DEFAULT 'general', -- 'work', 'rest', 'social', 'general'
                priority INTEGER DEFAULT 1, -- 1-5优先级
                status TEXT DEFAULT 'scheduled', -- 'scheduled', 'active', 'completed', 'cancelled'
                created_at TEXT NOT NULL,
                FOREIGN KEY (agent_name) REFERENCES agent_configs(agent_name)
            )`
        ];
        
        for (const table of tables) {
            await this.dbRun(table);
        }
        
        // 创建索引
        const indexes = [
            `CREATE INDEX IF NOT EXISTS idx_psychology_user_agent ON psychology_activities(user_id, agent_name)`,
            `CREATE INDEX IF NOT EXISTS idx_psychology_timestamp ON psychology_activities(timestamp)`,
            `CREATE INDEX IF NOT EXISTS idx_psychology_session ON psychology_activities(session_id)`,
            `CREATE INDEX IF NOT EXISTS idx_schedule_agent_time ON schedule_events(agent_name, start_time)`,
            `CREATE INDEX IF NOT EXISTS idx_schedule_status ON schedule_events(status)`,
            `CREATE INDEX IF NOT EXISTS idx_user_agent_rel ON user_agent_relationships(user_id, agent_name)`,
            `CREATE INDEX IF NOT EXISTS idx_agent_snapshots ON agent_state_snapshots(user_id, agent_name, created_at)`,
            `CREATE INDEX IF NOT EXISTS idx_snapshots_type ON agent_state_snapshots(snapshot_type, created_at)`
        ];
        
        for (const index of indexes) {
            await this.dbRun(index);
        }
        
        this.logger.info('世界树系统', '数据库表创建完成');
    }

    /**
     * 加载Agent列表
     */
    async loadAgentList() {
        try {
            const agentDir = path.join(__dirname, '../../Agent');
            const files = await fs.readdir(agentDir);
            const agentFiles = files.filter(file => file.endsWith('.txt'));
            
            this.agentCache.clear();
            
            for (const file of agentFiles) {
                const agentName = path.basename(file, '.txt');
                const agentPath = path.join(agentDir, file);
                
                try {
                    const content = await fs.readFile(agentPath, 'utf-8');
                    this.agentCache.set(agentName, {
                        name: agentName,
                        content: content,
                        lastModified: (await fs.stat(agentPath)).mtime
                    });
                    
                    // 确保数据库中有对应的配置记录
                    await this.ensureAgentConfig(agentName);
                    
                } catch (fileError) {
                    this.logger.warn('世界树系统', `读取Agent文件失败: ${file}`, fileError.message);
                }
            }
            
            this.logger.info('世界树系统', `加载了 ${this.agentCache.size} 个Agent配置`);
            
        } catch (error) {
            this.logger.error('世界树系统', '加载Agent列表失败:', error);
        }
    }

    /**
     * 确保Agent配置存在
     */
    async ensureAgentConfig(agentName) {
        const existing = await this.dbGet(
            'SELECT * FROM agent_configs WHERE agent_name = ?',
            [agentName]
        );
        
        if (!existing) {
            const now = this.getLocalTimeString();
            await this.dbRun(`
                INSERT INTO agent_configs (
                    agent_name, display_name, time_architecture, schedule_data,
                    personality_traits, background_story, last_updated, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                agentName,
                agentName,
                JSON.stringify(this.getDefaultTimeArchitecture()),
                JSON.stringify([]),
                JSON.stringify({}),
                '',
                now,
                now
            ]);
            
            this.logger.info('世界树系统', `为Agent ${agentName} 创建了默认配置`);
        }
    }

    /**
     * 获取默认时间架构
     */
    getDefaultTimeArchitecture() {
        return {
            timezone: 'Asia/Shanghai',
            workingHours: {
                start: '09:00',
                end: '18:00'
            },
            restHours: {
                start: '22:00',
                end: '07:00'
            },
            activeHours: {
                start: '07:00',
                end: '22:00'
            }
        };
    }

    /**
     * 获取本地时间字符串
     */
    getLocalTimeString() {
        return new Date().toLocaleString('zh-CN', {
            timeZone: 'Asia/Shanghai',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        }).replace(/\//g, '-');
    }

    /**
     * 启动日程更新器
     */
    startScheduleUpdater() {
        if (this.scheduleTimer) {
            clearInterval(this.scheduleTimer);
        }
        
        this.scheduleTimer = setInterval(() => {
            this.updateScheduleStatus();
        }, this.scheduleInterval * 60 * 1000);
        
        this.logger.info('世界树系统', `日程更新器已启动，间隔: ${this.scheduleInterval}分钟`);
    }

    /**
     * 更新日程状态
     */
    async updateScheduleStatus() {
        try {
            const now = new Date();
            const nowString = this.getLocalTimeString();
            
            // 更新过期的scheduled事件为completed
            await this.dbRun(`
                UPDATE schedule_events 
                SET status = 'completed' 
                WHERE status = 'scheduled' 
                AND datetime(end_time) < datetime(?)
            `, [nowString]);
            
            // 更新当前时间的事件为active
            await this.dbRun(`
                UPDATE schedule_events 
                SET status = 'active' 
                WHERE status = 'scheduled' 
                AND datetime(start_time) <= datetime(?) 
                AND datetime(end_time) > datetime(?)
            `, [nowString, nowString]);
            
            if (this.debugMode) {
                this.logger.debug('世界树系统', '日程状态更新完成');
            }
            
        } catch (error) {
            this.logger.error('世界树系统', '更新日程状态失败:', error);
        }
    }

    /**
     * 为指定角色生成system消息增强内容
     */
    async generateSystemEnhancement(agentName, userId, conversationContext = {}) {
        if (!this.isInitialized) {
            return '';
        }

        try {
            // 获取角色配置
            const agentConfig = await this.getAgentConfig(agentName);
            if (!agentConfig) {
                return '';
            }

            // 获取当前时间信息
            const timeInfo = this.getCurrentTimeInfo(agentConfig.time_architecture);

            // 获取当前日程
            const currentSchedule = await this.getCurrentSchedule(agentName);

            // 生成心理活动（如果启用）
            let psychologyContent = '';
            if (this.psychologyEnabled) {
                psychologyContent = await this.generatePsychologyActivity(
                    agentName, userId, conversationContext
                );
            }

            // 构建system增强内容
            const enhancement = this.buildSystemEnhancement({
                agentConfig,
                timeInfo,
                currentSchedule,
                psychologyContent
            });

            return enhancement;

        } catch (error) {
            this.logger.error('世界树系统', `生成system增强失败 (${agentName}):`, error);
            return '';
        }
    }

    /**
     * 获取角色配置
     */
    async getAgentConfig(agentName) {
        try {
            const config = await this.dbGet(
                'SELECT * FROM agent_configs WHERE agent_name = ?',
                [agentName]
            );

            if (config) {
                // 解析JSON字段
                config.time_architecture = JSON.parse(config.time_architecture || '{}');
                config.schedule_data = JSON.parse(config.schedule_data || '[]');
                config.personality_traits = JSON.parse(config.personality_traits || '{}');
            }

            return config;

        } catch (error) {
            this.logger.error('世界树系统', `获取角色配置失败 (${agentName}):`, error);
            return null;
        }
    }

    /**
     * 获取当前时间信息
     */
    getCurrentTimeInfo(timeArchitecture) {
        const now = new Date();
        const timeString = this.getLocalTimeString();
        const hour = now.getHours();
        const minute = now.getMinutes();

        // 判断当前时间段
        let timePhase = 'unknown';
        if (timeArchitecture.workingHours) {
            const workStart = parseInt(timeArchitecture.workingHours.start.split(':')[0]);
            const workEnd = parseInt(timeArchitecture.workingHours.end.split(':')[0]);

            if (hour >= workStart && hour < workEnd) {
                timePhase = 'working';
            }
        }

        if (timeArchitecture.restHours) {
            const restStart = parseInt(timeArchitecture.restHours.start.split(':')[0]);
            const restEnd = parseInt(timeArchitecture.restHours.end.split(':')[0]);

            if (hour >= restStart || hour < restEnd) {
                timePhase = 'resting';
            }
        }

        if (timePhase === 'unknown' && timeArchitecture.activeHours) {
            const activeStart = parseInt(timeArchitecture.activeHours.start.split(':')[0]);
            const activeEnd = parseInt(timeArchitecture.activeHours.end.split(':')[0]);

            if (hour >= activeStart && hour < activeEnd) {
                timePhase = 'active';
            }
        }

        return {
            currentTime: timeString,
            timePhase: timePhase,
            hour: hour,
            minute: minute
        };
    }

    /**
     * 获取当前日程
     */
    async getCurrentSchedule(agentName) {
        try {
            const now = this.getLocalTimeString();

            // 获取当前活跃的事件
            const activeEvents = await this.dbAll(`
                SELECT * FROM schedule_events
                WHERE agent_name = ?
                AND status = 'active'
                ORDER BY start_time
            `, [agentName]);

            // 获取即将到来的事件（未来2小时内）
            const upcomingEvents = await this.dbAll(`
                SELECT * FROM schedule_events
                WHERE agent_name = ?
                AND status = 'scheduled'
                AND datetime(start_time) BETWEEN datetime(?) AND datetime(?, '+2 hours')
                ORDER BY start_time
                LIMIT 3
            `, [agentName, now, now]);

            return {
                active: activeEvents,
                upcoming: upcomingEvents
            };

        } catch (error) {
            this.logger.error('世界树系统', `获取当前日程失败 (${agentName}):`, error);
            return { active: [], upcoming: [] };
        }
    }

    /**
     * 生成心理活动
     */
    async generatePsychologyActivity(agentName, userId, conversationContext) {
        if (!this.openaiConfig.apiKey) {
            return '';
        }

        try {
            // 获取用户的情感记忆数据
            const emotionalData = await this.getEmotionalMemoryData(userId, agentName);

            // 构建心理分析提示
            const prompt = this.buildPsychologyPrompt(agentName, emotionalData, conversationContext);

            // 调用OpenAI API
            const response = await this.callOpenAI(prompt);

            // 存储心理活动记录
            if (response) {
                await this.storePsychologyActivity(userId, agentName, response, conversationContext);
            }

            return response || '';

        } catch (error) {
            this.logger.error('世界树系统', `生成心理活动失败 (${agentName}):`, error);
            return '';
        }
    }

    /**
     * 获取情感记忆数据
     */
    async getEmotionalMemoryData(userId, agentName) {
        try {
            const emotionalData = await this.emotionalConnector.getEmotionalData(userId, agentName);
            const analysis = this.emotionalConnector.analyzeEmotionalState(emotionalData);

            // 转换为心理分析所需的格式
            return {
                stress_level: emotionalData.stress ? emotionalData.stress.stress_value : 0.0,
                emotion_valence: emotionalData.affinity ? emotionalData.affinity.emotion_valence : 0.0,
                emotion_arousal: emotionalData.affinity ? emotionalData.affinity.emotion_arousal : 0.0,
                emotion_dominance: emotionalData.affinity ? emotionalData.affinity.emotion_dominance : 0.0,
                recent_interactions: emotionalData.recentInteractions ? emotionalData.recentInteractions.length : 0,
                affinity_level: emotionalData.affinity ? emotionalData.affinity.current_affinity : 0.5,
                relationship_type: emotionalData.affinity ? emotionalData.affinity.relationship_type : 'neutral',
                current_emotion: emotionalData.emotions ? emotionalData.emotions.current_emotion : 'neutral',
                overall_mood: analysis.overallMood,
                social_connection: analysis.socialConnection,
                cognitive_state: analysis.cognitiveState,
                memory_fragments_count: emotionalData.memoryFragments ? emotionalData.memoryFragments.length : 0
            };

        } catch (error) {
            this.logger.error('世界树系统', '获取情感记忆数据失败:', error);
            return this.emotionalConnector.getDefaultEmotionalData();
        }
    }

    /**
     * 构建心理分析提示
     */
    buildPsychologyPrompt(agentName, emotionalData, conversationContext) {
        const currentTime = this.getLocalTimeString();

        return `作为AI角色${agentName}，基于以下信息生成当前的心理活动和内在想法：

当前时间: ${currentTime}

情感状态数据：
- 压力等级: ${(emotionalData.stress_level * 100).toFixed(1)}%
- 情绪效价: ${emotionalData.emotion_valence?.toFixed(2) || '0.00'} (正负情绪倾向)
- 情绪唤醒: ${emotionalData.emotion_arousal?.toFixed(2) || '0.00'} (情绪激活程度)
- 情绪支配: ${emotionalData.emotion_dominance?.toFixed(2) || '0.00'} (控制感)
- 好感度: ${(emotionalData.affinity_level * 100).toFixed(1)}%
- 关系类型: ${emotionalData.relationship_type || 'neutral'}
- 当前情绪: ${emotionalData.current_emotion || 'neutral'}
- 整体心情: ${emotionalData.overall_mood || 'neutral'}
- 社交连接: ${emotionalData.social_connection || 'neutral'}
- 认知状态: ${emotionalData.cognitive_state || 'stable'}

交互历史：
- 最近交互次数: ${emotionalData.recent_interactions || 0}
- 记忆片段数量: ${emotionalData.memory_fragments_count || 0}

对话上下文：
- 消息数量: ${conversationContext.messageCount || 0}
- 包含图片: ${conversationContext.hasImages ? '是' : '否'}
- 最后用户消息: ${conversationContext.lastUserMessage || '无'}

请基于以上数据生成简洁的心理活动描述（30-50字），体现角色的内在状态、情绪反应和想法。要求：
1. 符合角色性格特征
2. 反映当前情感状态
3. 考虑时间和上下文因素
4. 语言自然流畅`;
    }

    /**
     * 调用OpenAI API
     */
    async callOpenAI(prompt) {
        try {
            const fetch = require('node-fetch');

            const response = await fetch(`${this.openaiConfig.baseURL}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.openaiConfig.apiKey}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    model: this.openaiConfig.model,
                    messages: [
                        {
                            role: 'user',
                            content: prompt
                        }
                    ],
                    max_tokens: 100,
                    temperature: 0.7
                })
            });

            const data = await response.json();

            if (data.choices && data.choices[0] && data.choices[0].message) {
                return data.choices[0].message.content.trim();
            }

            return null;

        } catch (error) {
            this.logger.error('世界树系统', 'OpenAI API调用失败:', error);
            return null;
        }
    }

    /**
     * 存储心理活动记录
     */
    async storePsychologyActivity(userId, agentName, content, context) {
        try {
            await this.dbRun(`
                INSERT INTO psychology_activities (
                    user_id, agent_name, activity_type, content,
                    trigger_context, timestamp
                ) VALUES (?, ?, ?, ?, ?, ?)
            `, [
                userId,
                agentName,
                'thought',
                content,
                JSON.stringify(context),
                this.getLocalTimeString()
            ]);

        } catch (error) {
            this.logger.error('世界树系统', '存储心理活动失败:', error);
        }
    }

    /**
     * 构建system增强内容
     */
    buildSystemEnhancement({ agentConfig, timeInfo, currentSchedule, psychologyContent }) {
        let enhancement = '\n\n=== 世界树角色状态 ===\n';

        // 时间信息
        enhancement += `当前时间: ${timeInfo.currentTime}\n`;
        enhancement += `时间阶段: ${this.getTimePhaseDescription(timeInfo.timePhase)}\n`;

        // 角色状态
        if (agentConfig.current_mood !== 'neutral') {
            enhancement += `当前心情: ${agentConfig.current_mood}\n`;
        }

        if (agentConfig.stress_level > 0.1) {
            enhancement += `压力等级: ${(agentConfig.stress_level * 100).toFixed(0)}%\n`;
        }

        if (agentConfig.energy_level < 0.9) {
            enhancement += `精力状态: ${(agentConfig.energy_level * 100).toFixed(0)}%\n`;
        }

        // 当前日程
        if (currentSchedule.active.length > 0) {
            enhancement += `正在进行: ${currentSchedule.active[0].event_title}\n`;
        }

        if (currentSchedule.upcoming.length > 0) {
            enhancement += `即将到来: ${currentSchedule.upcoming[0].event_title} (${currentSchedule.upcoming[0].start_time})\n`;
        }

        // 心理活动
        if (psychologyContent) {
            enhancement += `内心想法: ${psychologyContent}\n`;
        }

        enhancement += '=== 请根据以上状态调整你的回应风格和内容 ===\n';

        return enhancement;
    }

    /**
     * 获取时间阶段描述
     */
    getTimePhaseDescription(phase) {
        const descriptions = {
            'working': '工作时间',
            'resting': '休息时间',
            'active': '活跃时间',
            'unknown': '未知时间段'
        };

        return descriptions[phase] || '未知时间段';
    }

    /**
     * 销毁插件
     */
    async destroy() {
        if (this.scheduleTimer) {
            clearInterval(this.scheduleTimer);
            this.scheduleTimer = null;
        }

        if (this.emotionalConnector) {
            await this.emotionalConnector.disconnect();
        }

        if (this.db) {
            this.db.close();
            this.db = null;
        }

        this.isInitialized = false;
        this.logger.info('世界树系统', '世界树系统已销毁');
    }
}

// 插件导出
module.exports = {
    WorldTreeCore,
    initializePlugin,
    destroyPlugin
};
