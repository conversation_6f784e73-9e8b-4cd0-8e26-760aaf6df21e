const express = require('express');

/**
 * 世界树API路由
 * 提供角色管理、日程管理等API接口
 */
class WorldTreeAPI {
    constructor(worldTreeManager, logger) {
        this.manager = worldTreeManager;
        this.logger = logger;
        this.router = express.Router();
        
        this.setupRoutes();
    }

    /**
     * 设置API路由
     */
    setupRoutes() {
        // 获取所有角色列表
        this.router.get('/agents', async (req, res) => {
            try {
                const agents = await this.manager.getAllAgents();
                res.json({
                    success: true,
                    data: agents
                });
            } catch (error) {
                this.logger.error('世界树API', '获取角色列表失败:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // 获取指定角色配置
        this.router.get('/agents/:agentName', async (req, res) => {
            try {
                const { agentName } = req.params;
                const config = await this.manager.getAgentConfig(agentName);
                
                if (!config) {
                    return res.status(404).json({
                        success: false,
                        error: '角色不存在'
                    });
                }
                
                res.json({
                    success: true,
                    data: {
                        ...config,
                        time_architecture: JSON.parse(config.time_architecture || '{}'),
                        schedule_data: JSON.parse(config.schedule_data || '[]'),
                        personality_traits: JSON.parse(config.personality_traits || '{}')
                    }
                });
            } catch (error) {
                this.logger.error('世界树API', '获取角色配置失败:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // 更新角色配置
        this.router.put('/agents/:agentName', async (req, res) => {
            try {
                const { agentName } = req.params;
                const configData = req.body;
                
                await this.manager.updateAgentConfig(agentName, configData);
                
                res.json({
                    success: true,
                    message: `角色 ${agentName} 配置已更新`
                });
            } catch (error) {
                this.logger.error('世界树API', '更新角色配置失败:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // 获取角色日程事件
        this.router.get('/agents/:agentName/schedule', async (req, res) => {
            try {
                const { agentName } = req.params;
                const { startDate, endDate } = req.query;
                
                const events = await this.manager.getScheduleEvents(agentName, startDate, endDate);
                
                res.json({
                    success: true,
                    data: events
                });
            } catch (error) {
                this.logger.error('世界树API', '获取日程事件失败:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // 添加日程事件
        this.router.post('/agents/:agentName/schedule', async (req, res) => {
            try {
                const { agentName } = req.params;
                const eventData = req.body;
                
                // 验证必需字段
                if (!eventData.title || !eventData.start_time) {
                    return res.status(400).json({
                        success: false,
                        error: '缺少必需字段: title, start_time'
                    });
                }
                
                await this.manager.addScheduleEvent(agentName, eventData);
                
                res.json({
                    success: true,
                    message: `为角色 ${agentName} 添加了日程事件`
                });
            } catch (error) {
                this.logger.error('世界树API', '添加日程事件失败:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // 删除日程事件
        this.router.delete('/schedule/:eventId', async (req, res) => {
            try {
                const { eventId } = req.params;
                
                if (!this.manager.isEnabled || !this.manager.core) {
                    return res.status(503).json({
                        success: false,
                        error: '世界树系统未启用'
                    });
                }
                
                await this.manager.core.dbRun(
                    'DELETE FROM schedule_events WHERE id = ?',
                    [eventId]
                );
                
                res.json({
                    success: true,
                    message: '日程事件已删除'
                });
            } catch (error) {
                this.logger.error('世界树API', '删除日程事件失败:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // 更新日程事件
        this.router.put('/schedule/:eventId', async (req, res) => {
            try {
                const { eventId } = req.params;
                const eventData = req.body;
                
                if (!this.manager.isEnabled || !this.manager.core) {
                    return res.status(503).json({
                        success: false,
                        error: '世界树系统未启用'
                    });
                }
                
                await this.manager.core.dbRun(`
                    UPDATE schedule_events SET
                        event_title = ?,
                        event_description = ?,
                        start_time = ?,
                        end_time = ?,
                        event_type = ?,
                        priority = ?,
                        status = ?
                    WHERE id = ?
                `, [
                    eventData.title,
                    eventData.description || '',
                    eventData.start_time,
                    eventData.end_time || null,
                    eventData.type || 'general',
                    eventData.priority || 1,
                    eventData.status || 'scheduled',
                    eventId
                ]);
                
                res.json({
                    success: true,
                    message: '日程事件已更新'
                });
            } catch (error) {
                this.logger.error('世界树API', '更新日程事件失败:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // 获取角色心理活动记录
        this.router.get('/agents/:agentName/psychology', async (req, res) => {
            try {
                const { agentName } = req.params;
                const { userId, limit = 10 } = req.query;
                
                if (!this.manager.isEnabled || !this.manager.core) {
                    return res.status(503).json({
                        success: false,
                        error: '世界树系统未启用'
                    });
                }
                
                let query = 'SELECT * FROM psychology_activities WHERE agent_name = ?';
                let params = [agentName];
                
                if (userId) {
                    query += ' AND user_id = ?';
                    params.push(userId);
                }
                
                query += ' ORDER BY timestamp DESC LIMIT ?';
                params.push(parseInt(limit));
                
                const activities = await this.manager.core.dbAll(query, params);
                
                res.json({
                    success: true,
                    data: activities
                });
            } catch (error) {
                this.logger.error('世界树API', '获取心理活动记录失败:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // 获取系统状态
        this.router.get('/status', async (req, res) => {
            try {
                const status = {
                    enabled: this.manager.isEnabled,
                    initialized: this.manager.core ? this.manager.core.isInitialized : false,
                    agentCount: this.manager.core ? this.manager.core.agentCache.size : 0,
                    psychologyEnabled: this.manager.core ? this.manager.core.psychologyEnabled : false
                };
                
                res.json({
                    success: true,
                    data: status
                });
            } catch (error) {
                this.logger.error('世界树API', '获取系统状态失败:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // 手动触发心理活动分析
        this.router.post('/agents/:agentName/psychology/analyze', async (req, res) => {
            try {
                const { agentName } = req.params;
                const { userId, context } = req.body;

                if (!this.manager.isEnabled || !this.manager.core) {
                    return res.status(503).json({
                        success: false,
                        error: '世界树系统未启用'
                    });
                }

                const psychologyContent = await this.manager.core.generatePsychologyActivity(
                    agentName,
                    userId || 'manual_trigger',
                    context || {}
                );

                res.json({
                    success: true,
                    data: {
                        content: psychologyContent,
                        timestamp: this.manager.core.getLocalTimeString()
                    }
                });
            } catch (error) {
                this.logger.error('世界树API', '手动心理分析失败:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // 获取用户的角色关系列表
        this.router.get('/users/:userId/relationships', async (req, res) => {
            try {
                const { userId } = req.params;

                const relationships = await this.manager.getUserAgentRelationships(userId);

                res.json({
                    success: true,
                    data: relationships
                });
            } catch (error) {
                this.logger.error('世界树API', '获取用户角色关系失败:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // 获取角色的用户列表
        this.router.get('/agents/:agentName/users', async (req, res) => {
            try {
                const { agentName } = req.params;

                const users = await this.manager.getAgentUsers(agentName);

                res.json({
                    success: true,
                    data: users
                });
            } catch (error) {
                this.logger.error('世界树API', '获取角色用户列表失败:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // 创建状态快照
        this.router.post('/users/:userId/agents/:agentName/snapshots', async (req, res) => {
            try {
                const { userId, agentName } = req.params;
                const { snapshotType = 'manual' } = req.body;

                await this.manager.createStateSnapshot(userId, agentName, snapshotType);

                res.json({
                    success: true,
                    message: `为用户 ${userId} 和角色 ${agentName} 创建了状态快照`
                });
            } catch (error) {
                this.logger.error('世界树API', '创建状态快照失败:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // 获取状态快照列表
        this.router.get('/users/:userId/agents/:agentName/snapshots', async (req, res) => {
            try {
                const { userId, agentName } = req.params;
                const { snapshotType, limit = 10 } = req.query;

                const snapshots = await this.manager.getStateSnapshots(
                    userId,
                    agentName,
                    snapshotType,
                    parseInt(limit)
                );

                res.json({
                    success: true,
                    data: snapshots
                });
            } catch (error) {
                this.logger.error('世界树API', '获取状态快照失败:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });

        // 获取用户-角色的心理活动记录
        this.router.get('/users/:userId/agents/:agentName/psychology', async (req, res) => {
            try {
                const { userId, agentName } = req.params;
                const { limit = 10 } = req.query;

                const activities = await this.manager.getPsychologyActivities(
                    userId,
                    agentName,
                    parseInt(limit)
                );

                res.json({
                    success: true,
                    data: activities
                });
            } catch (error) {
                this.logger.error('世界树API', '获取心理活动记录失败:', error);
                res.status(500).json({
                    success: false,
                    error: error.message
                });
            }
        });
    }

    /**
     * 获取路由器
     */
    getRouter() {
        return this.router;
    }
}

module.exports = WorldTreeAPI;
