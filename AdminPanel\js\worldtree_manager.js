/**
 * 世界树角色管理系统前端界面
 */
class WorldTreeManager {
    constructor() {
        this.currentAgent = null;
        this.agents = [];
        this.scheduleEvents = [];
        this.psychologyActivities = [];
        
        this.initializeEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        // 刷新按钮
        document.getElementById('worldtree-refresh-btn')?.addEventListener('click', () => {
            this.loadAgents();
        });

        // 保存配置按钮
        document.getElementById('worldtree-save-config-btn')?.addEventListener('click', () => {
            this.saveAgentConfig();
        });

        // 添加日程按钮
        document.getElementById('worldtree-add-schedule-btn')?.addEventListener('click', () => {
            this.showAddScheduleModal();
        });

        // 心理分析按钮
        document.getElementById('worldtree-analyze-psychology-btn')?.addEventListener('click', () => {
            this.triggerPsychologyAnalysis();
        });
    }

    /**
     * 初始化世界树管理器
     */
    async initialize() {
        try {
            await this.loadSystemStatus();
            await this.loadAgents();
            
            console.log('[WorldTree] 世界树管理器初始化完成');
        } catch (error) {
            console.error('[WorldTree] 初始化失败:', error);
            this.showError('初始化失败: ' + error.message);
        }
    }

    /**
     * 加载系统状态
     */
    async loadSystemStatus() {
        try {
            const response = await fetch('/admin_api/worldtree/status');
            const result = await response.json();
            
            if (result.success) {
                this.updateStatusDisplay(result.data);
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('[WorldTree] 加载系统状态失败:', error);
            this.updateStatusDisplay({ enabled: false, initialized: false });
        }
    }

    /**
     * 更新状态显示
     */
    updateStatusDisplay(status) {
        const statusElement = document.getElementById('worldtree-status');
        if (statusElement) {
            const statusText = status.enabled && status.initialized ? '运行中' : '未启用';
            const statusClass = status.enabled && status.initialized ? 'text-green-600' : 'text-red-600';
            
            statusElement.innerHTML = `
                <span class="${statusClass}">● ${statusText}</span>
                <span class="text-gray-500 ml-2">
                    角色数量: ${status.agentCount || 0} | 
                    心理分析: ${status.psychologyEnabled ? '启用' : '禁用'}
                </span>
            `;
        }
    }

    /**
     * 加载角色列表
     */
    async loadAgents() {
        try {
            const response = await fetch('/admin_api/worldtree/agents');
            const result = await response.json();
            
            if (result.success) {
                this.agents = result.data;
                this.renderAgentList();
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('[WorldTree] 加载角色列表失败:', error);
            this.showError('加载角色列表失败: ' + error.message);
        }
    }

    /**
     * 渲染角色列表
     */
    renderAgentList() {
        const container = document.getElementById('worldtree-agent-list');
        if (!container) return;

        if (this.agents.length === 0) {
            container.innerHTML = '<div class="text-gray-500 text-center py-4">暂无角色配置</div>';
            return;
        }

        const html = this.agents.map(agent => `
            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                 onclick="worldTreeManager.selectAgent('${agent.agent_name}')">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="font-medium text-gray-900">${agent.display_name || agent.agent_name}</h4>
                        <p class="text-sm text-gray-500">
                            心情: ${agent.current_mood} | 
                            压力: ${(agent.stress_level * 100).toFixed(0)}% | 
                            精力: ${(agent.energy_level * 100).toFixed(0)}%
                        </p>
                        <p class="text-xs text-gray-400">最后更新: ${agent.last_updated}</p>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            ${agent.agent_name}
                        </span>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    /**
     * 选择角色
     */
    async selectAgent(agentName) {
        try {
            this.currentAgent = agentName;
            
            // 加载角色详细配置
            await this.loadAgentConfig(agentName);
            
            // 加载日程事件
            await this.loadScheduleEvents(agentName);
            
            // 加载心理活动记录
            await this.loadPsychologyActivities(agentName);
            
            // 显示配置面板
            this.showConfigPanel();
            
        } catch (error) {
            console.error('[WorldTree] 选择角色失败:', error);
            this.showError('选择角色失败: ' + error.message);
        }
    }

    /**
     * 加载角色配置
     */
    async loadAgentConfig(agentName) {
        const response = await fetch(`/admin_api/worldtree/agents/${agentName}`);
        const result = await response.json();
        
        if (result.success) {
            this.renderAgentConfig(result.data);
        } else {
            throw new Error(result.error);
        }
    }

    /**
     * 渲染角色配置
     */
    renderAgentConfig(config) {
        // 基本信息
        document.getElementById('agent-display-name').value = config.display_name || config.agent_name;
        document.getElementById('agent-background-story').value = config.background_story || '';
        document.getElementById('agent-current-mood').value = config.current_mood || 'neutral';
        document.getElementById('agent-stress-level').value = config.stress_level || 0;
        document.getElementById('agent-energy-level').value = config.energy_level || 1;

        // 时间架构
        const timeArch = config.time_architecture || {};
        document.getElementById('time-timezone').value = timeArch.timezone || 'Asia/Shanghai';
        
        if (timeArch.workingHours) {
            document.getElementById('working-start').value = timeArch.workingHours.start || '09:00';
            document.getElementById('working-end').value = timeArch.workingHours.end || '18:00';
        }
        
        if (timeArch.restHours) {
            document.getElementById('rest-start').value = timeArch.restHours.start || '22:00';
            document.getElementById('rest-end').value = timeArch.restHours.end || '07:00';
        }

        // 性格特征
        const traits = config.personality_traits || {};
        document.getElementById('personality-traits').value = JSON.stringify(traits, null, 2);
    }

    /**
     * 保存角色配置
     */
    async saveAgentConfig() {
        if (!this.currentAgent) {
            this.showError('请先选择一个角色');
            return;
        }

        try {
            const configData = {
                display_name: document.getElementById('agent-display-name').value,
                background_story: document.getElementById('agent-background-story').value,
                current_mood: document.getElementById('agent-current-mood').value,
                stress_level: parseFloat(document.getElementById('agent-stress-level').value),
                energy_level: parseFloat(document.getElementById('agent-energy-level').value),
                time_architecture: {
                    timezone: document.getElementById('time-timezone').value,
                    workingHours: {
                        start: document.getElementById('working-start').value,
                        end: document.getElementById('working-end').value
                    },
                    restHours: {
                        start: document.getElementById('rest-start').value,
                        end: document.getElementById('rest-end').value
                    }
                },
                personality_traits: JSON.parse(document.getElementById('personality-traits').value || '{}')
            };

            const response = await fetch(`/admin_api/worldtree/agents/${this.currentAgent}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(configData)
            });

            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('配置保存成功');
                await this.loadAgents(); // 刷新列表
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('[WorldTree] 保存配置失败:', error);
            this.showError('保存配置失败: ' + error.message);
        }
    }

    /**
     * 加载日程事件
     */
    async loadScheduleEvents(agentName) {
        try {
            const response = await fetch(`/admin_api/worldtree/agents/${agentName}/schedule`);
            const result = await response.json();
            
            if (result.success) {
                this.scheduleEvents = result.data;
                this.renderScheduleEvents();
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('[WorldTree] 加载日程事件失败:', error);
            this.scheduleEvents = [];
            this.renderScheduleEvents();
        }
    }

    /**
     * 渲染日程事件
     */
    renderScheduleEvents() {
        const container = document.getElementById('schedule-events-list');
        if (!container) return;

        if (this.scheduleEvents.length === 0) {
            container.innerHTML = '<div class="text-gray-500 text-center py-4">暂无日程事件</div>';
            return;
        }

        const html = this.scheduleEvents.map(event => `
            <div class="border border-gray-200 rounded-lg p-3 mb-2">
                <div class="flex items-center justify-between">
                    <div>
                        <h5 class="font-medium text-gray-900">${event.event_title}</h5>
                        <p class="text-sm text-gray-600">${event.event_description || ''}</p>
                        <p class="text-xs text-gray-500">
                            ${event.start_time} ${event.end_time ? '- ' + event.end_time : ''}
                        </p>
                    </div>
                    <div class="text-right">
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                               ${this.getStatusColor(event.status)}">
                            ${event.status}
                        </span>
                        <div class="mt-1">
                            <button onclick="worldTreeManager.deleteScheduleEvent(${event.id})" 
                                    class="text-red-600 hover:text-red-800 text-xs">删除</button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    /**
     * 获取状态颜色
     */
    getStatusColor(status) {
        const colors = {
            'scheduled': 'bg-blue-100 text-blue-800',
            'active': 'bg-green-100 text-green-800',
            'completed': 'bg-gray-100 text-gray-800',
            'cancelled': 'bg-red-100 text-red-800'
        };
        return colors[status] || 'bg-gray-100 text-gray-800';
    }

    /**
     * 显示配置面板
     */
    showConfigPanel() {
        const panel = document.getElementById('worldtree-config-panel');
        if (panel) {
            panel.classList.remove('hidden');
        }
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        // 这里可以集成现有的消息显示系统
        console.log('[WorldTree] Success:', message);
        alert('成功: ' + message);
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        // 这里可以集成现有的错误显示系统
        console.error('[WorldTree] Error:', message);
        alert('错误: ' + message);
    }

    /**
     * 加载用户角色关系
     */
    async loadUserAgentRelationships(userId) {
        try {
            const response = await fetch(`/admin_api/worldtree/users/${userId}/relationships`);
            const result = await response.json();

            if (result.success) {
                return result.data;
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('[WorldTree] 加载用户角色关系失败:', error);
            return [];
        }
    }

    /**
     * 加载角色用户列表
     */
    async loadAgentUsers(agentName) {
        try {
            const response = await fetch(`/admin_api/worldtree/agents/${agentName}/users`);
            const result = await response.json();

            if (result.success) {
                return result.data;
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('[WorldTree] 加载角色用户列表失败:', error);
            return [];
        }
    }

    /**
     * 创建状态快照
     */
    async createStateSnapshot(userId, agentName, snapshotType = 'manual') {
        try {
            const response = await fetch(`/admin_api/worldtree/users/${userId}/agents/${agentName}/snapshots`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ snapshotType })
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccess('状态快照创建成功');
                return true;
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('[WorldTree] 创建状态快照失败:', error);
            this.showError('创建状态快照失败: ' + error.message);
            return false;
        }
    }

    /**
     * 加载状态快照列表
     */
    async loadStateSnapshots(userId, agentName, snapshotType = null) {
        try {
            let url = `/admin_api/worldtree/users/${userId}/agents/${agentName}/snapshots`;
            if (snapshotType) {
                url += `?snapshotType=${snapshotType}`;
            }

            const response = await fetch(url);
            const result = await response.json();

            if (result.success) {
                return result.data;
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('[WorldTree] 加载状态快照失败:', error);
            return [];
        }
    }

    /**
     * 更新心理活动加载方法以支持用户ID
     */
    async loadPsychologyActivities(agentName, userId = 'default_user') {
        try {
            const response = await fetch(`/admin_api/worldtree/users/${userId}/agents/${agentName}/psychology`);
            const result = await response.json();

            if (result.success) {
                this.psychologyActivities = result.data;
                this.renderPsychologyActivities();
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('[WorldTree] 加载心理活动记录失败:', error);
            this.psychologyActivities = [];
            this.renderPsychologyActivities();
        }
    }

    /**
     * 渲染心理活动记录
     */
    renderPsychologyActivities() {
        const container = document.getElementById('psychology-activities-list');
        if (!container) return;

        if (this.psychologyActivities.length === 0) {
            container.innerHTML = '<div class="text-gray-500 text-center py-4">暂无心理活动记录</div>';
            return;
        }

        const html = this.psychologyActivities.map(activity => `
            <div class="border border-gray-200 rounded-lg p-3 mb-2">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <h5 class="font-medium text-gray-900">${activity.activity_type}</h5>
                        <p class="text-sm text-gray-600 mt-1">${activity.content}</p>
                        <p class="text-xs text-gray-500 mt-2">
                            ${activity.timestamp} | 置信度: ${(activity.confidence_score * 100).toFixed(0)}%
                        </p>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = html;
    }
}

// 创建全局实例
const worldTreeManager = new WorldTreeManager();
