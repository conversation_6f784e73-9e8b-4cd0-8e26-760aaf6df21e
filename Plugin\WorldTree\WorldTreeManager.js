const WorldTreeCore = require('./WorldTreeCore');
const path = require('path');

/**
 * 世界树管理器 - 与主系统集成的接口
 */
class WorldTreeManager {
    constructor() {
        this.core = null;
        this.logger = null;
        this.isEnabled = false;
    }

    /**
     * 初始化管理器
     */
    async initialize(config, logger) {
        this.logger = logger;
        
        try {
            this.core = new WorldTreeCore(config, logger);
            await this.core.initialize();
            this.isEnabled = true;
            
            this.logger.success('世界树管理器', '✅ 世界树管理器初始化完成');
            
        } catch (error) {
            this.logger.error('世界树管理器', '初始化失败:', error);
            this.isEnabled = false;
            throw error;
        }
    }

    /**
     * 检查是否为指定角色提供增强
     */
    shouldEnhanceAgent(agentName) {
        if (!this.isEnabled || !this.core) {
            return false;
        }
        
        // 检查缓存中是否有该角色
        return this.core.agentCache.has(agentName);
    }

    /**
     * 为消息添加世界树增强
     * 这是主要的集成接口，会被server.js调用
     */
    async enhanceSystemMessage(originalBody, cleanedBody) {
        if (!this.isEnabled || !this.core) {
            return;
        }
        
        try {
            // 获取角色名称
            const agentName = originalBody.agent;
            if (!agentName || !this.shouldEnhanceAgent(agentName)) {
                return;
            }
            
            // 获取用户ID
            const userId = this.extractUserId(originalBody, cleanedBody);
            if (!userId) {
                return;
            }
            
            // 构建对话上下文
            const conversationContext = this.buildConversationContext(cleanedBody);
            
            // 生成增强内容
            const enhancement = await this.core.generateSystemEnhancement(
                agentName, 
                userId, 
                conversationContext
            );
            
            if (enhancement) {
                // 添加到system消息
                this.injectSystemEnhancement(cleanedBody, enhancement);
                
                this.logger.info('世界树管理器', `为角色 ${agentName} 注入了世界树增强内容`);
            }
            
        } catch (error) {
            this.logger.error('世界树管理器', '增强system消息失败:', error);
        }
    }

    /**
     * 提取用户ID
     */
    extractUserId(originalBody, cleanedBody) {
        // 尝试从多个可能的位置获取用户ID
        return originalBody.userId || 
               originalBody.user_id || 
               originalBody.userID ||
               'default_user';
    }

    /**
     * 构建对话上下文
     */
    buildConversationContext(cleanedBody) {
        const context = {
            messageCount: cleanedBody.messages ? cleanedBody.messages.length : 0,
            hasImages: false,
            lastUserMessage: '',
            conversationType: 'chat'
        };
        
        if (cleanedBody.messages && cleanedBody.messages.length > 0) {
            // 获取最后一条用户消息
            const userMessages = cleanedBody.messages.filter(msg => msg.role === 'user');
            if (userMessages.length > 0) {
                const lastMsg = userMessages[userMessages.length - 1];
                
                if (typeof lastMsg.content === 'string') {
                    context.lastUserMessage = lastMsg.content.substring(0, 200);
                } else if (Array.isArray(lastMsg.content)) {
                    // 处理多模态消息
                    const textParts = lastMsg.content.filter(part => part.type === 'text');
                    if (textParts.length > 0) {
                        context.lastUserMessage = textParts[0].text.substring(0, 200);
                    }
                    
                    const imageParts = lastMsg.content.filter(part => part.type === 'image_url');
                    context.hasImages = imageParts.length > 0;
                }
            }
        }
        
        return context;
    }

    /**
     * 注入system增强内容
     */
    injectSystemEnhancement(cleanedBody, enhancement) {
        if (!cleanedBody.messages) {
            cleanedBody.messages = [];
        }
        
        // 查找现有的system消息
        let hasSystemMessage = false;
        cleanedBody.messages = cleanedBody.messages.map(msg => {
            if (msg.role === 'system') {
                hasSystemMessage = true;
                return {
                    ...msg,
                    content: msg.content + enhancement
                };
            }
            return msg;
        });
        
        // 如果没有system消息，创建一个新的
        if (!hasSystemMessage) {
            cleanedBody.messages.unshift({
                role: 'system',
                content: `你是一个智能助手。${enhancement}`
            });
        }
    }

    /**
     * 获取角色配置（用于管理面板）
     */
    async getAgentConfig(agentName) {
        if (!this.isEnabled || !this.core) {
            return null;
        }
        
        return await this.core.getAgentConfig(agentName);
    }

    /**
     * 更新角色配置（用于管理面板）
     */
    async updateAgentConfig(agentName, configData) {
        if (!this.isEnabled || !this.core) {
            throw new Error('世界树系统未启用');
        }
        
        try {
            const now = this.core.getLocalTimeString();
            
            await this.core.dbRun(`
                UPDATE agent_configs SET
                    display_name = ?,
                    time_architecture = ?,
                    schedule_data = ?,
                    personality_traits = ?,
                    background_story = ?,
                    current_mood = ?,
                    stress_level = ?,
                    energy_level = ?,
                    last_updated = ?
                WHERE agent_name = ?
            `, [
                configData.display_name || agentName,
                JSON.stringify(configData.time_architecture || {}),
                JSON.stringify(configData.schedule_data || []),
                JSON.stringify(configData.personality_traits || {}),
                configData.background_story || '',
                configData.current_mood || 'neutral',
                configData.stress_level || 0.0,
                configData.energy_level || 1.0,
                now,
                agentName
            ]);
            
            this.logger.info('世界树管理器', `更新了角色 ${agentName} 的配置`);
            
        } catch (error) {
            this.logger.error('世界树管理器', `更新角色配置失败 (${agentName}):`, error);
            throw error;
        }
    }

    /**
     * 获取所有角色列表（用于管理面板）
     */
    async getAllAgents() {
        if (!this.isEnabled || !this.core) {
            return [];
        }
        
        try {
            const agents = await this.core.dbAll('SELECT * FROM agent_configs ORDER BY agent_name');
            
            return agents.map(agent => ({
                ...agent,
                time_architecture: JSON.parse(agent.time_architecture || '{}'),
                schedule_data: JSON.parse(agent.schedule_data || '[]'),
                personality_traits: JSON.parse(agent.personality_traits || '{}')
            }));
            
        } catch (error) {
            this.logger.error('世界树管理器', '获取角色列表失败:', error);
            return [];
        }
    }

    /**
     * 添加日程事件
     */
    async addScheduleEvent(agentName, eventData) {
        if (!this.isEnabled || !this.core) {
            throw new Error('世界树系统未启用');
        }
        
        try {
            const now = this.core.getLocalTimeString();
            
            await this.core.dbRun(`
                INSERT INTO schedule_events (
                    agent_name, event_title, event_description, start_time,
                    end_time, event_type, priority, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `, [
                agentName,
                eventData.title,
                eventData.description || '',
                eventData.start_time,
                eventData.end_time || null,
                eventData.type || 'general',
                eventData.priority || 1,
                now
            ]);
            
            this.logger.info('世界树管理器', `为角色 ${agentName} 添加了日程事件: ${eventData.title}`);
            
        } catch (error) {
            this.logger.error('世界树管理器', `添加日程事件失败 (${agentName}):`, error);
            throw error;
        }
    }

    /**
     * 获取角色的日程事件
     */
    async getScheduleEvents(agentName, startDate, endDate) {
        if (!this.isEnabled || !this.core) {
            return [];
        }
        
        try {
            let query = 'SELECT * FROM schedule_events WHERE agent_name = ?';
            let params = [agentName];
            
            if (startDate && endDate) {
                query += ' AND start_time BETWEEN ? AND ?';
                params.push(startDate, endDate);
            }
            
            query += ' ORDER BY start_time';
            
            return await this.core.dbAll(query, params);
            
        } catch (error) {
            this.logger.error('世界树管理器', `获取日程事件失败 (${agentName}):`, error);
            return [];
        }
    }

    /**
     * 销毁管理器
     */
    async destroy() {
        if (this.core) {
            await this.core.destroy();
            this.core = null;
        }
        
        this.isEnabled = false;
        
        if (this.logger) {
            this.logger.info('世界树管理器', '世界树管理器已销毁');
        }
    }
}

module.exports = WorldTreeManager;
