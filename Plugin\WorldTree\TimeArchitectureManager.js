/**
 * 时间架构管理器
 * 处理角色的时间规律、生物钟和活动模式
 */
class TimeArchitectureManager {
    constructor(logger) {
        this.logger = logger;
    }

    /**
     * 创建默认时间架构
     */
    createDefaultArchitecture() {
        return {
            timezone: 'Asia/Shanghai',
            workingHours: {
                start: '09:00',
                end: '18:00',
                description: '工作时间段'
            },
            restHours: {
                start: '22:00',
                end: '07:00',
                description: '休息睡眠时间段'
            },
            activeHours: {
                start: '07:00',
                end: '22:00',
                description: '活跃时间段'
            },
            mealTimes: {
                breakfast: '07:30',
                lunch: '12:00',
                dinner: '18:30'
            },
            energyPattern: {
                peak: ['09:00', '14:00', '19:00'], // 精力高峰时段
                low: ['13:00', '15:00', '21:00']   // 精力低谷时段
            },
            socialPreferences: {
                preferredContactHours: {
                    start: '09:00',
                    end: '21:00'
                },
                avoidContactHours: {
                    start: '22:00',
                    end: '08:00'
                }
            },
            weeklyPattern: {
                monday: { mood: 'focused', energy: 0.8 },
                tuesday: { mood: 'productive', energy: 0.9 },
                wednesday: { mood: 'steady', energy: 0.7 },
                thursday: { mood: 'motivated', energy: 0.8 },
                friday: { mood: 'relaxed', energy: 0.6 },
                saturday: { mood: 'free', energy: 0.5 },
                sunday: { mood: 'peaceful', energy: 0.4 }
            }
        };
    }

    /**
     * 验证时间架构配置
     */
    validateArchitecture(architecture) {
        const errors = [];

        // 验证必需字段
        if (!architecture.timezone) {
            errors.push('缺少时区设置');
        }

        // 验证时间格式
        const timeFields = [
            'workingHours.start', 'workingHours.end',
            'restHours.start', 'restHours.end',
            'activeHours.start', 'activeHours.end'
        ];

        for (const field of timeFields) {
            const value = this.getNestedValue(architecture, field);
            if (value && !this.isValidTimeFormat(value)) {
                errors.push(`无效的时间格式: ${field} = ${value}`);
            }
        }

        // 验证时间逻辑
        if (architecture.workingHours) {
            const start = this.timeToMinutes(architecture.workingHours.start);
            const end = this.timeToMinutes(architecture.workingHours.end);
            
            if (start >= end) {
                errors.push('工作开始时间不能晚于或等于结束时间');
            }
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * 获取嵌套对象的值
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : null;
        }, obj);
    }

    /**
     * 验证时间格式 (HH:MM)
     */
    isValidTimeFormat(timeStr) {
        const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
        return timeRegex.test(timeStr);
    }

    /**
     * 将时间字符串转换为分钟数
     */
    timeToMinutes(timeStr) {
        const [hours, minutes] = timeStr.split(':').map(Number);
        return hours * 60 + minutes;
    }

    /**
     * 根据时间架构分析当前状态
     */
    analyzeCurrentState(architecture) {
        const now = new Date();
        const currentTime = now.toLocaleTimeString('zh-CN', {
            timeZone: architecture.timezone || 'Asia/Shanghai',
            hour12: false,
            hour: '2-digit',
            minute: '2-digit'
        });
        
        const currentMinutes = this.timeToMinutes(currentTime);
        const dayOfWeek = now.toLocaleDateString('en-US', { weekday: 'lowercase' });

        const state = {
            currentTime: currentTime,
            dayOfWeek: dayOfWeek,
            timePhase: this.determineTimePhase(currentMinutes, architecture),
            energyLevel: this.calculateEnergyLevel(currentMinutes, architecture),
            socialAvailability: this.checkSocialAvailability(currentMinutes, architecture),
            weeklyMood: architecture.weeklyPattern ? architecture.weeklyPattern[dayOfWeek] : null,
            upcomingTransitions: this.getUpcomingTransitions(currentMinutes, architecture)
        };

        return state;
    }

    /**
     * 确定当前时间段
     */
    determineTimePhase(currentMinutes, architecture) {
        const phases = [];

        // 检查工作时间
        if (architecture.workingHours) {
            const start = this.timeToMinutes(architecture.workingHours.start);
            const end = this.timeToMinutes(architecture.workingHours.end);
            
            if (this.isTimeInRange(currentMinutes, start, end)) {
                phases.push('working');
            }
        }

        // 检查休息时间
        if (architecture.restHours) {
            const start = this.timeToMinutes(architecture.restHours.start);
            const end = this.timeToMinutes(architecture.restHours.end);
            
            if (this.isTimeInRange(currentMinutes, start, end, true)) {
                phases.push('resting');
            }
        }

        // 检查活跃时间
        if (architecture.activeHours) {
            const start = this.timeToMinutes(architecture.activeHours.start);
            const end = this.timeToMinutes(architecture.activeHours.end);
            
            if (this.isTimeInRange(currentMinutes, start, end)) {
                phases.push('active');
            }
        }

        return phases.length > 0 ? phases : ['unknown'];
    }

    /**
     * 检查时间是否在范围内
     */
    isTimeInRange(currentMinutes, startMinutes, endMinutes, allowOvernight = false) {
        if (allowOvernight && startMinutes > endMinutes) {
            // 跨夜的时间段 (如 22:00 - 07:00)
            return currentMinutes >= startMinutes || currentMinutes <= endMinutes;
        } else {
            // 正常时间段
            return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
        }
    }

    /**
     * 计算当前精力等级
     */
    calculateEnergyLevel(currentMinutes, architecture) {
        let baseEnergy = 0.5; // 基础精力

        // 根据精力模式调整
        if (architecture.energyPattern) {
            const { peak, low } = architecture.energyPattern;
            
            // 检查是否在高峰时段
            for (const peakTime of peak || []) {
                const peakMinutes = this.timeToMinutes(peakTime);
                const distance = Math.abs(currentMinutes - peakMinutes);
                
                if (distance <= 60) { // 1小时内
                    baseEnergy += 0.3 * (1 - distance / 60);
                }
            }
            
            // 检查是否在低谷时段
            for (const lowTime of low || []) {
                const lowMinutes = this.timeToMinutes(lowTime);
                const distance = Math.abs(currentMinutes - lowMinutes);
                
                if (distance <= 60) { // 1小时内
                    baseEnergy -= 0.2 * (1 - distance / 60);
                }
            }
        }

        return Math.max(0, Math.min(1, baseEnergy));
    }

    /**
     * 检查社交可用性
     */
    checkSocialAvailability(currentMinutes, architecture) {
        if (!architecture.socialPreferences) {
            return true;
        }

        const { preferredContactHours, avoidContactHours } = architecture.socialPreferences;

        // 检查避免联系时间
        if (avoidContactHours) {
            const start = this.timeToMinutes(avoidContactHours.start);
            const end = this.timeToMinutes(avoidContactHours.end);
            
            if (this.isTimeInRange(currentMinutes, start, end, true)) {
                return false;
            }
        }

        // 检查偏好联系时间
        if (preferredContactHours) {
            const start = this.timeToMinutes(preferredContactHours.start);
            const end = this.timeToMinutes(preferredContactHours.end);
            
            return this.isTimeInRange(currentMinutes, start, end);
        }

        return true;
    }

    /**
     * 获取即将到来的时间段转换
     */
    getUpcomingTransitions(currentMinutes, architecture) {
        const transitions = [];
        const timePoints = [];

        // 收集所有时间点
        if (architecture.workingHours) {
            timePoints.push({
                time: architecture.workingHours.start,
                type: 'work_start',
                description: '开始工作'
            });
            timePoints.push({
                time: architecture.workingHours.end,
                type: 'work_end',
                description: '结束工作'
            });
        }

        if (architecture.restHours) {
            timePoints.push({
                time: architecture.restHours.start,
                type: 'rest_start',
                description: '开始休息'
            });
            timePoints.push({
                time: architecture.restHours.end,
                type: 'rest_end',
                description: '结束休息'
            });
        }

        // 找到接下来的转换点
        for (const point of timePoints) {
            const pointMinutes = this.timeToMinutes(point.time);
            let timeDiff = pointMinutes - currentMinutes;
            
            // 处理跨夜情况
            if (timeDiff < 0) {
                timeDiff += 24 * 60; // 加一天
            }
            
            if (timeDiff > 0 && timeDiff <= 4 * 60) { // 4小时内
                transitions.push({
                    ...point,
                    minutesUntil: timeDiff,
                    hoursUntil: Math.floor(timeDiff / 60),
                    minutesRemaining: timeDiff % 60
                });
            }
        }

        return transitions.sort((a, b) => a.minutesUntil - b.minutesUntil);
    }

    /**
     * 生成时间架构描述文本
     */
    generateArchitectureDescription(architecture) {
        const state = this.analyzeCurrentState(architecture);
        let description = '';

        description += `当前时间: ${state.currentTime}\n`;
        description += `时间段: ${state.timePhase.join(', ')}\n`;
        description += `精力等级: ${(state.energyLevel * 100).toFixed(0)}%\n`;
        description += `社交可用: ${state.socialAvailability ? '是' : '否'}\n`;

        if (state.weeklyMood) {
            description += `今日状态: ${state.weeklyMood.mood} (精力${(state.weeklyMood.energy * 100).toFixed(0)}%)\n`;
        }

        if (state.upcomingTransitions.length > 0) {
            const next = state.upcomingTransitions[0];
            description += `即将: ${next.description} (${next.hoursUntil}小时${next.minutesRemaining}分钟后)\n`;
        }

        return description;
    }
}

module.exports = TimeArchitectureManager;
