// server.js
const express = require('express');
const dotenv = require('dotenv');
const schedule = require('node-schedule');

const fsSync = require('fs'); // Renamed to fsSync for clarity with fs.promises
const fs = require('fs').promises; // fs.promises for async operations
const path = require('path');
const { Writable } = require('stream');
const FormData = require('form-data'); // 用于文件类型检测

// 引入组件模块
const { formatBeijingTime, getTimeAgo, formatBytes, formatUptime } = require('./components/timeUtils');
const { formatToolAnalysisForSystem } = require('./components/toolAnalysis');
const { recordEmotionMemory, generateIntelligentContext } = require('./components/memorySystem');
const ConfigHotReloadManager = require('./utils/configHotReload');

const { processMessagesVariableReplacement } = require('./components/variableReplacer');
const { processUserMessage, processAgentParameter, processUserAgentParameter, processChatContextParameter, processMcpAgentParameter, processMcpUserAgentParameter, processMcpChatContextParameter, cleanRequestBody } = require('./components/messageProcessor');







// 引入现代化日志系统
const logger = require('./utils/logger.cjs');

const DEBUG_LOG_DIR = path.join(__dirname, 'DebugLog'); // Moved DEBUG_LOG_DIR definition higher
let currentServerLogPath = '';
let serverLogWriteStream = null;

// 确保 DebugLog 目录存在 (同步版本，因为在启动时需要)
function ensureDebugLogDirSync() {
    if (!fsSync.existsSync(DEBUG_LOG_DIR)) {
        try {
            fsSync.mkdirSync(DEBUG_LOG_DIR, { recursive: true });
            // Use originalConsoleLog if available, otherwise logger.debug (it might not be overridden yet)
            logger.system(`调试日志目录已创建: ${DEBUG_LOG_DIR}`);
        } catch (error) {
            logger.error('服务器设置', `创建调试日志目录失败: ${DEBUG_LOG_DIR}`, error);
        }
    }
}

// 服务器启动时调用
function initializeServerLogger() {
    ensureDebugLogDirSync(); // 确保目录存在
    const now = new Date();
    const timestamp = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}_${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}${now.getSeconds().toString().padStart(2, '0')}_${now.getMilliseconds().toString().padStart(3, '0')}`;
    currentServerLogPath = path.join(DEBUG_LOG_DIR, `ServerLog-${timestamp}.txt`);
    
    try {
        fsSync.writeFileSync(currentServerLogPath, `[${new Date().toLocaleString()}] Server log started.\n`, 'utf-8');
        serverLogWriteStream = fsSync.createWriteStream(currentServerLogPath, { flags: 'a' });
        logger.system(`服务器日志将记录到: ${currentServerLogPath}`);
    } catch (error) {
        logger.error('服务器设置', `初始化服务器日志文件失败: ${currentServerLogPath}`, error);
        serverLogWriteStream = null;
    }
}

// 保存原始 console 方法
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
const originalConsoleInfo = console.info;

// 在服务器启动的最开始就初始化日志记录器
initializeServerLogger(); // Call this before console methods are overridden if they log during init


function formatLogMessage(level, args) {
    const timestamp = new Date().toLocaleString();
    // Handle potential circular structures in objects for JSON.stringify
    const safeStringify = (obj) => {
        const cache = new Set();
        return JSON.stringify(obj, (key, value) => {
            if (typeof value === 'object' && value !== null) {
                if (cache.has(value)) {
                    return '[Circular]';
                }
                cache.add(value);
            }
            return value;
        }, 2);
    };
    const message = args.map(arg => (typeof arg === 'object' ? safeStringify(arg) : String(arg))).join(' ');
    return `[${timestamp}] [${level.toUpperCase()}] ${message}\n`;
}

function writeToLogFile(formattedMessage) {
    if (serverLogWriteStream) {
        serverLogWriteStream.write(formattedMessage, (err) => {
            if (err) {
                originalConsoleError('[Logger] 写入日志文件失败:', err);
            }
        });
    }
}

console.log = (...args) => {
    originalConsoleLog.apply(console, args);
    const formattedMessage = formatLogMessage('log', args);
    writeToLogFile(formattedMessage);
};

console.error = (...args) => {
    originalConsoleError.apply(console, args);
    const formattedMessage = formatLogMessage('error', args);
    writeToLogFile(formattedMessage);
};

console.warn = (...args) => {
    originalConsoleWarn.apply(console, args);
    const formattedMessage = formatLogMessage('warn', args);
    writeToLogFile(formattedMessage);
};

console.info = (...args) => {
    originalConsoleInfo.apply(console, args);
    const formattedMessage = formatLogMessage('info', args);
    writeToLogFile(formattedMessage);
};


const AGENT_DIR = path.join(__dirname, 'Agent'); // 定义 Agent 目录
const crypto = require('crypto');
const pluginManager = require('./Plugin.js');
const McpManager = require('./Plugin/Mcp/McpManager.js'); // 新增 MCP管理器
const webSocketServer = require('./WebSocketServer.js'); // 新增 WebSocketServer 引入
const basicAuth = require('basic-auth');
const cors = require('cors'); // 引入 cors 模块

// 创建MCP管理器实例
const mcpManager = new McpManager();
mcpManager.setPluginManager(pluginManager);

// 设置全局引用以便MCP插件使用
global.pluginManager = pluginManager;
global.mcpManager = mcpManager;

dotenv.config({ path: 'config.env' });

const ADMIN_USERNAME = process.env.AdminUsername;
const ADMIN_PASSWORD = process.env.AdminPassword;

const DEBUG_MODE = (process.env.DebugMode || "False").toLowerCase() === "true";
const SHOW_VCP_OUTPUT = (process.env.ShowVCP || "False").toLowerCase() === "true"; // 读取 ShowVCP 环境变量

// === MCP/OpenAI Tools 支持系统配置 ===
const OPENAI_TOOLS_URL = process.env.OPENAI_TOOLS_URL;
const OPENAI_TOOLS_KEY = process.env.OPENAI_TOOLS_KEY;
const OPENAI_TOOLS_MODEL = process.env.OPENAI_TOOLS_MODEL || 'gpt-4o-mini';
const VCP_USE_OPENAI_TOOLS = (process.env.VCP_USE_OPENAI_TOOLS || "false").toLowerCase() === "true";

// MCP工具系统将动态加载插件，不再需要静态映射表

// ensureDebugLogDir is now ensureDebugLogDirSync and called by initializeServerLogger() synchronously earlier.
// writeDebugLog remains for specific debug purposes, it uses fs.promises.
async function ensureDebugLogDirAsync() { // Renamed to avoid conflict, used by writeDebugLog
    if (DEBUG_MODE) {
        try {
            await fs.mkdir(DEBUG_LOG_DIR, { recursive: true });
        } catch (error) {
            logger.error('服务器设置', `创建调试日志目录失败 (异步): ${DEBUG_LOG_DIR}`, error);
        }
    }
}

async function writeDebugLog(filenamePrefix, data) {
    if (DEBUG_MODE) {
        await ensureDebugLogDirAsync(); // Use the async version here
        const now = new Date();
        const timestamp = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}_${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}${now.getSeconds().toString().padStart(2, '0')}_${now.getMilliseconds().toString().padStart(3, '0')}`;
        const filename = `${filenamePrefix}-${timestamp}.txt`;
        const filePath = path.join(DEBUG_LOG_DIR, filename);
        try {
            await fs.writeFile(filePath, JSON.stringify(data, null, 2));
            logger.info('调试日志', `已记录日志: ${filename}`);
        } catch (error) {
            logger.error('调试日志', `写入调试日志失败: ${filePath}`, error);
        }
    }
}

const detectors = [];
for (const key in process.env) {
    if (/^Detector\d+$/.test(key)) {
        const index = key.substring(8);
        const outputKey = `Detector_Output${index}`;
        if (process.env[outputKey]) {
            detectors.push({ detector: process.env[key], output: process.env[outputKey] });
        }
    }
}
// 显示启动横幅
logger.banner();

if (detectors.length > 0) logger.system(`共加载了 ${detectors.length} 条系统提示词转换规则`);
else logger.system('未加载任何系统提示词转换规则');

const superDetectors = [];
for (const key in process.env) {
    if (/^SuperDetector\d+$/.test(key)) {
        const index = key.substring(13);
        const outputKey = `SuperDetector_Output${index}`;
        if (process.env[outputKey]) {
            superDetectors.push({ detector: process.env[key], output: process.env[outputKey] });
        }
    }
}
if (superDetectors.length > 0) logger.system(`共加载了 ${superDetectors.length} 条全局上下文转换规则`);
else logger.system('未加载任何全局上下文转换规则');

// 显示MCP模式配置状态
if (VCP_USE_OPENAI_TOOLS) {
    logger.success('MCP工具', 'MCP模式已启用 - 使用OpenAI Tools替代VCP判断');
    logger.info('MCP工具', `OpenAI服务URL: ${OPENAI_TOOLS_URL || '未配置'}`);
    logger.info('MCP工具', `OpenAI模型: ${OPENAI_TOOLS_MODEL}`);
    logger.info('MCP工具', `API密钥: ${OPENAI_TOOLS_KEY ? '已配置' : '未配置'}`);
    // MCP插件数量将在初始化后显示
} else {
    logger.warning('MCP工具', 'MCP模式未启用 - 仅支持传统VCP模式');
}

const app = express();
app.use(cors()); // 启用 CORS，允许跨域请求
const port = process.env.PORT;
const apiKey = process.env.API_Key;
const apiUrl = process.env.API_URL;
const serverKey = process.env.Key;

const cachedEmojiLists = new Map();

app.use(express.json({ limit: '300mb' }));
app.use(express.urlencoded({ limit: '300mb', extended: true }));

// 确保必要的目录存在
const IMAGE_DIR = path.join(__dirname, 'image');
const TEMP_DIR = path.join(__dirname, 'temp');

// 确保目录存在的函数
function ensureDirectoryExists(dirPath) {
    if (!fsSync.existsSync(dirPath)) {
        try {
            fsSync.mkdirSync(dirPath, { recursive: true });
            logger.system(`目录已创建: ${dirPath}`);
        } catch (error) {
            logger.error('服务器设置', `创建目录失败: ${dirPath}`, error);
        }
    }
}

// 在服务器启动时创建必要的目录
ensureDirectoryExists(IMAGE_DIR);
ensureDirectoryExists(TEMP_DIR);

// 定期清理旧的图片文件
schedule.scheduleJob('0 */6 * * *', async () => { // 每6小时执行一次
    try {
        const files = await fs.readdir(IMAGE_DIR);
        const now = Date.now();
        const maxAge = 24 * 60 * 60 * 1000; // 24小时
        let cleanedCount = 0;

        for (const file of files) {
            const filePath = path.join(IMAGE_DIR, file);
            try {
                const stats = await fs.stat(filePath);
                // 只处理文件，不处理文件夹
                if (stats.isFile() && (now - stats.mtime.getTime() > maxAge)) {
                    await fs.unlink(filePath);
                    cleanedCount++;
                    logger.info('文件清理', `已删除旧图片: ${file}`);
                } else if (stats.isDirectory()) {
                    if (DEBUG_MODE) {
                        logger.info('文件清理', `跳过文件夹: ${file}`);
                    }
                }
            } catch (error) {
                logger.error('文件清理', `处理文件失败: ${file}`, error);
            }
        }
        logger.info('文件清理', `图片清理任务完成，清理了 ${cleanedCount} 个文件`);
    } catch (error) {
        logger.error('文件清理', '图片清理任务失败', error);
    }
});

// 清理临时文件
schedule.scheduleJob('*/30 * * * *', async () => { // 每30分钟执行一次
    try {
        const files = await fs.readdir(TEMP_DIR);
        const now = Date.now();
        const maxAge = 30 * 60 * 1000; // 30分钟
        let cleanedCount = 0;

        for (const file of files) {
            const filePath = path.join(TEMP_DIR, file);
            try {
                const stats = await fs.stat(filePath);
                // 只处理文件，不处理文件夹
                if (stats.isFile() && (now - stats.mtime.getTime() > maxAge)) {
                    await fs.unlink(filePath);
                    cleanedCount++;
                    logger.info('文件清理', `已删除临时文件: ${file}`);
                } else if (stats.isDirectory()) {
                    if (DEBUG_MODE) {
                        logger.info('文件清理', `跳过文件夹: ${file}`);
                    }
                }
            } catch (error) {
                logger.error('文件清理', `处理临时文件失败: ${file}`, error);
            }
        }
        logger.info('文件清理', `临时文件清理任务完成，清理了 ${cleanedCount} 个文件`);
    } catch (error) {
        logger.error('文件清理', '临时文件清理任务失败', error);
    }
});

// 添加静态文件服务，用于访问图片
app.use('/image', express.static(IMAGE_DIR));

// 添加Live2D静态文件服务（无需认证）
app.use('/live2d', express.static(path.join(__dirname, 'live2d')));

// 添加项目根目录的静态文件服务（用于本地文件访问）
app.use(express.static(__dirname));

// === 新增：OpenAI Tools API端点 ===
// 获取可用的OpenAI Tools列表
app.get('/v1/tools', (req, res) => {
    try {
        if (!VCP_USE_OPENAI_TOOLS) {
            return res.status(400).json({
                error: 'TOOLS_DISABLED',
                message: 'OpenAI工具功能未启用',
                available: false
            });
        }
        
        const tools = mcpManager.getOpenAITools();
        
        res.json({
            available: true,
            tools_count: tools.length,
            tools: tools,
            config: {
                mcp_mode_enabled: VCP_USE_OPENAI_TOOLS,
                openai_tools_url: OPENAI_TOOLS_URL || '未配置',
                openai_tools_model: OPENAI_TOOLS_MODEL,
                openai_tools_key_configured: !!OPENAI_TOOLS_KEY
            }
        });
        
        logger.info('API接口', `返回${tools.length}个可用的OpenAI工具`);
        
    } catch (error) {
        logger.error('API接口', `获取工具列表失败: ${error.message}`);
        res.status(500).json({
            error: 'TOOLS_LIST_ERROR',
            message: error.message
        });
    }
});

// 获取MCP插件信息
app.get('/v1/tools/mapping', (req, res) => {
    try {
        const vcpPlugins = pluginManager.getAvailablePlugins();
        const mcpPlugins = mcpManager.getAvailableMcpPlugins();
        
        const mappingInfo = {
            vcp_plugins: vcpPlugins.map(name => ({
                name: name,
                type: 'vcp',
                has_mcp_equivalent: mcpPlugins.some(mcp => mcp.name === name || mcpPlugins.find(m => m.vcpName === name))
            })),
            mcp_plugins: mcpPlugins.map(plugin => ({
                name: plugin.name,
                description: plugin.description,
                type: 'mcp',
                enabled: plugin.enabled,
                vcp_equivalent: plugin.vcpName || null
            }))
        };
        
        res.json({
            mapping: mappingInfo,
            total_vcp_plugins: vcpPlugins.length,
            total_mcp_plugins: mcpPlugins.length,
            enabled_mcp_plugins: mcpPlugins.filter(p => p.enabled).length
        });
        
    } catch (error) {
        logger.error('API接口', `获取插件映射信息失败: ${error.message}`);
        res.status(500).json({
            error: 'MAPPING_INFO_ERROR',
            message: error.message
        });
    }
});

// === 新增：管理面板API接口 ===
// 获取Agent列表和内容
app.get('/admin_api/agents', (req, res) => {
    try {
        const agentDir = path.join(__dirname, 'Agent');
        const agents = [];

        if (fsSync.existsSync(agentDir)) {
            const files = fsSync.readdirSync(agentDir);

            for (const file of files) {
                if (path.extname(file).toLowerCase() === '.txt') {
                    const agentName = path.basename(file, '.txt');
                    const agentPath = path.join(agentDir, file);

                    try {
                        const content = fsSync.readFileSync(agentPath, 'utf-8');
                        const stats = fsSync.statSync(agentPath);

                        agents.push({
                            name: agentName,
                            filename: file,
                            content: content,
                            size: stats.size,
                            lastModified: stats.mtime,
                            created: stats.birthtime
                        });
                    } catch (error) {
                        logger.warning('Agent API', `读取Agent文件失败: ${file}`, error.message);
                    }
                }
            }
        }

        res.json({
            success: true,
            count: agents.length,
            agents: agents
        });

        logger.info('Admin API', `返回了${agents.length}个Agent信息`);

    } catch (error) {
        logger.error('Admin API', 'Agent列表获取失败', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 获取Live2D模型列表
app.get('/admin_api/live2d-models', (req, res) => {
    try {
        const live2dDir = path.join(__dirname, 'live2d', 'model');
        const models = [];

        if (fsSync.existsSync(live2dDir)) {
            const modelDirs = fsSync.readdirSync(live2dDir);

            for (const modelDir of modelDirs) {
                const modelPath = path.join(live2dDir, modelDir);
                const stats = fsSync.statSync(modelPath);

                if (stats.isDirectory()) {
                    const modelInfo = {
                        name: modelDir,
                        path: modelPath,
                        lastModified: stats.mtime,
                        files: []
                    };

                    try {
                        // 检查模型配置文件
                        const modelJsonPath = path.join(modelPath, 'model.json');
                        if (fsSync.existsSync(modelJsonPath)) {
                            const modelConfig = JSON.parse(fsSync.readFileSync(modelJsonPath, 'utf-8'));
                            modelInfo.config = modelConfig;
                            modelInfo.hasConfig = true;
                        } else {
                            modelInfo.hasConfig = false;
                        }

                        // 列出模型文件
                        const files = fsSync.readdirSync(modelPath);
                        modelInfo.files = files;
                        modelInfo.fileCount = files.length;

                    } catch (error) {
                        logger.warning('Live2D API', `读取模型配置失败: ${modelDir}`, error.message);
                        modelInfo.error = error.message;
                    }

                    models.push(modelInfo);
                }
            }
        }

        res.json({
            success: true,
            count: models.length,
            models: models
        });

        logger.info('Admin API', `返回了${models.length}个Live2D模型信息`);

    } catch (error) {
        logger.error('Admin API', 'Live2D模型列表获取失败', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 获取服务器日志
app.get('/admin_api/server-logs', (req, res) => {
    try {
        const logDir = path.join(__dirname, 'DebugLog');
        const lines = parseInt(req.query.lines) || 100; // 默认返回最后100行
        const logFiles = [];

        if (fsSync.existsSync(logDir)) {
            const files = fsSync.readdirSync(logDir);

            // 获取最新的服务器日志文件 - 修正文件名模式
            const serverLogFiles = files.filter(file => file.startsWith('ServerLog-'));
            serverLogFiles.sort((a, b) => {
                const statA = fsSync.statSync(path.join(logDir, a));
                const statB = fsSync.statSync(path.join(logDir, b));
                return statB.mtime - statA.mtime; // 最新的在前
            });

            if (serverLogFiles.length > 0) {
                const latestLogFile = path.join(logDir, serverLogFiles[0]);
                const logContent = fsSync.readFileSync(latestLogFile, 'utf-8');
                const logLines = logContent.split('\n').filter(line => line.trim());

                // 返回最后N行
                const recentLines = logLines.slice(-lines);

                res.json({
                    success: true,
                    logFile: serverLogFiles[0],
                    totalLines: logLines.length,
                    returnedLines: recentLines.length,
                    logs: recentLines
                });
            } else {
                res.json({
                    success: true,
                    logFile: null,
                    totalLines: 0,
                    returnedLines: 0,
                    logs: []
                });
            }
        } else {
            res.json({
                success: false,
                error: '日志目录不存在'
            });
        }

    } catch (error) {
        logger.error('Admin API', '服务器日志获取失败', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 获取系统状态信息
app.get('/admin_api/system-status', (req, res) => {
    try {
        const memoryUsage = process.memoryUsage();
        const uptime = process.uptime();

        res.json({
            success: true,
            status: {
                uptime: uptime,
                uptimeFormatted: formatUptime(uptime),
                memory: {
                    rss: formatBytes(memoryUsage.rss),
                    heapTotal: formatBytes(memoryUsage.heapTotal),
                    heapUsed: formatBytes(memoryUsage.heapUsed),
                    external: formatBytes(memoryUsage.external)
                },
                nodeVersion: process.version,
                platform: process.platform,
                arch: process.arch,
                pid: process.pid,
                mode: VCP_USE_OPENAI_TOOLS ? 'MCP模式' : 'VCP模式',
                debugMode: DEBUG_MODE
            }
        });

    } catch (error) {
        logger.error('Admin API', '系统状态获取失败', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 获取详细系统监控信息
app.get('/admin_api/system-monitor', (req, res) => {
    try {
        const os = require('os');
        const memoryUsage = process.memoryUsage();
        const uptime = process.uptime();
        const systemUptime = os.uptime();

        // 获取系统内存信息
        const totalMemory = os.totalmem();
        const freeMemory = os.freemem();
        const usedMemory = totalMemory - freeMemory;

        // 获取CPU信息
        const cpus = os.cpus();
        const cpuCount = cpus.length;
        const cpuModel = cpus[0]?.model || 'Unknown';

        // 计算CPU使用率（简单估算）
        const loadAvg = os.loadavg();

        res.json({
            success: true,
            timestamp: formatBeijingTime(),
            system: {
                // 系统基本信息
                platform: process.platform,
                arch: process.arch,
                hostname: os.hostname(),
                nodeVersion: process.version,

                // 运行时间
                processUptime: uptime,
                processUptimeFormatted: formatUptime(uptime),
                systemUptime: systemUptime,
                systemUptimeFormatted: formatUptime(systemUptime),

                // 内存信息
                memory: {
                    // 进程内存
                    process: {
                        rss: memoryUsage.rss,
                        rssFormatted: formatBytes(memoryUsage.rss),
                        heapTotal: memoryUsage.heapTotal,
                        heapTotalFormatted: formatBytes(memoryUsage.heapTotal),
                        heapUsed: memoryUsage.heapUsed,
                        heapUsedFormatted: formatBytes(memoryUsage.heapUsed),
                        external: memoryUsage.external,
                        externalFormatted: formatBytes(memoryUsage.external),
                        heapUsagePercent: ((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100).toFixed(1)
                    },
                    // 系统内存
                    system: {
                        total: totalMemory,
                        totalFormatted: formatBytes(totalMemory),
                        used: usedMemory,
                        usedFormatted: formatBytes(usedMemory),
                        free: freeMemory,
                        freeFormatted: formatBytes(freeMemory),
                        usagePercent: ((usedMemory / totalMemory) * 100).toFixed(1)
                    }
                },

                // CPU信息
                cpu: {
                    count: cpuCount,
                    model: cpuModel,
                    loadAverage: loadAvg,
                    loadPercent: Math.min((loadAvg[0] / cpuCount) * 100, 100).toFixed(1)
                },

                // 应用状态
                application: {
                    mode: VCP_USE_OPENAI_TOOLS ? 'MCP模式' : 'VCP模式',
                    debugMode: DEBUG_MODE,
                    pid: process.pid,
                    port: SERVER_PORT || 6005
                }
            }
        });

    } catch (error) {
        logger.error('Admin API', '系统监控获取失败', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 配置热更新管理API
app.get('/admin_api/config/hot-reload/status', (req, res) => {
    try {
        if (!global.configHotReload) {
            return res.json({
                success: false,
                error: '配置热更新系统未初始化'
            });
        }

        const status = global.configHotReload.getStatus();
        res.json({
            success: true,
            data: status
        });

    } catch (error) {
        logger.error('配置热更新', '获取状态失败:', error.message);
        res.status(500).json({ error: '获取配置热更新状态失败' });
    }
});

app.get('/admin_api/config/hot-reload/configs', (req, res) => {
    try {
        if (!global.configHotReload) {
            return res.json({
                success: false,
                error: '配置热更新系统未初始化'
            });
        }

        const configs = global.configHotReload.getAllConfigs();
        res.json({
            success: true,
            data: configs
        });

    } catch (error) {
        logger.error('配置热更新', '获取配置失败:', error.message);
        res.status(500).json({ error: '获取配置列表失败' });
    }
});

app.post('/admin_api/config/hot-reload/reload/:configKey', async (req, res) => {
    try {
        const { configKey } = req.params;

        if (!global.configHotReload) {
            return res.json({
                success: false,
                error: '配置热更新系统未初始化'
            });
        }

        await global.configHotReload.manualReload(configKey);

        res.json({
            success: true,
            message: `配置 ${configKey} 已重新加载`
        });

    } catch (error) {
        logger.error('配置热更新', '手动重载配置失败:', error.message);
        res.status(500).json({ error: '重载配置失败: ' + error.message });
    }
});

// 获取配置更新日志
app.get('/admin_api/config/hot-reload/logs', (req, res) => {
    try {
        if (!global.configHotReload) {
            return res.json({
                success: false,
                error: '配置热更新系统未初始化'
            });
        }

        const limit = parseInt(req.query.limit) || 50;
        const logs = global.configHotReload.getUpdateLogs(limit);

        res.json({
            success: true,
            data: logs
        });

    } catch (error) {
        logger.error('配置热更新', '获取更新日志失败:', error.message);
        res.status(500).json({ error: '获取更新日志失败: ' + error.message });
    }
});

// 清空配置更新日志
app.delete('/admin_api/config/hot-reload/logs', (req, res) => {
    try {
        if (!global.configHotReload) {
            return res.json({
                success: false,
                error: '配置热更新系统未初始化'
            });
        }

        global.configHotReload.clearUpdateLogs();

        res.json({
            success: true,
            message: '更新日志已清空'
        });

    } catch (error) {
        logger.error('配置热更新', '清空更新日志失败:', error.message);
        res.status(500).json({ error: '清空更新日志失败: ' + error.message });
    }
});

// Authentication middleware for Admin Panel and Admin API
const adminAuth = (req, res, next) => {
    // 无需权限验证的API路径
    const noAuthPaths = [
        '/admin_api/wechat',
        '/admin_api/config/hot-reload',
        '/admin_api/memory/conversation/save',  // 微信适配器需要保存对话记录
        '/admin_api/memory/conversation/group',  // 微信适配器需要查询群聊历史
        '/admin_api/memory/conversation/private'  // 微信适配器需要查询私聊历史
    ];

    // 检查是否是无需认证的路径
    const isNoAuthPath = noAuthPaths.some(path => req.path.startsWith(path));
    if (isNoAuthPath) {
        return next();
    }

    const isAdminPath = req.path.startsWith('/AdminPanel') || req.path.startsWith('/admin_api');

    if (isAdminPath) {
        // Check if admin credentials are configured
        if (!ADMIN_USERNAME || !ADMIN_PASSWORD) {
                    logger.error('管理员认证', 'config.env 中未设置 AdminUsername 或 AdminPassword。管理面板已禁用');
            if (req.path.startsWith('/admin_api') || (req.headers.accept && req.headers.accept.includes('application/json'))) {
                 res.status(503).json({
                error: '服务不可用：管理员凭证未配置。',
                message: '请在 config.env 文件中设置 AdminUsername 和 AdminPassword 以启用管理面板。'
                });
            } else {
             res.status(503).send('<h1>503 服务不可用</h1><p>管理员凭证（AdminUsername、AdminPassword）未在 config.env 中配置。请配置它们以启用管理面板。</p>');
            }
            return; // Stop further processing
        }

        // Credentials are configured, proceed with Basic Auth
        const credentials = basicAuth(req);
        if (!credentials || credentials.name !== ADMIN_USERNAME || credentials.pass !== ADMIN_PASSWORD) {
            res.setHeader('WWW-Authenticate', 'Basic realm="Admin Panel"');
            if (req.path.startsWith('/admin_api') || (req.headers.accept && req.headers.accept.includes('application/json'))) {
                return res.status(401).json({ error: '未授权' });
            } else {
                return res.status(401).send('<h1>401 未授权</h1><p>访问管理面板需要身份验证。</p>');
            }
        }
        // Authentication successful
        return next();
    }
    // Not an admin path, proceed
    return next();
};
app.use(adminAuth); // Apply admin authentication globally (it will only act on /AdminPanel and /admin_api paths)

// Serve Admin Panel static files (will only be reached if adminAuth passes for /AdminPanel paths)
app.use('/AdminPanel', express.static(path.join(__dirname, 'AdminPanel')));


// Image server logic is now handled by the ImageServer plugin.

app.use((req, res, next) => {
    if (DEBUG_MODE) {
        logger.request(req.method, req.url, `来自 ${req.ip}`);
    }
    next();
});

// General API authentication (Bearer token) - This was the original one, now adminAuth handles its paths
app.use((req, res, next) => {
    // Skip bearer token check for admin panel API and static files, as they use basic auth or no auth
    if (req.path.startsWith('/admin_api') || req.path.startsWith('/AdminPanel')) {
        return next();
    }

    const imageServicePathRegex = /^\/pw=[^/]+\/images\//;
    if (imageServicePathRegex.test(req.path)) {
        return next();
    }

    // Skip bearer token check for plugin callbacks
    if (req.path.startsWith('/plugin-callback')) {
        return next();
    }

    const authHeader = req.headers.authorization;
    if (!authHeader || authHeader !== `Bearer ${serverKey}`) {
        return res.status(401).json({ error: '未授权 (需要 Bearer 令牌)' });
    }
    next();
});

// This function is no longer needed as the EmojiListGenerator plugin handles generation.
// async function updateAndLoadAgentEmojiList(agentName, dirPath, filePath) { ... }










// 新增：根据请求参数过滤或截断VCP工具请求响应的函数
function applyVcpFiltering(content, body) {
    const censorVcp = body.censor_vcp_output === true;
    const maxLength = body.vcp_content_max_length;

    if (DEBUG_MODE) {
        logger.info('VCP过滤', `应用过滤 - censor: ${censorVcp}, maxLength: ${maxLength}, 内容长度: ${content?.length || 0}`);
    }

    // 如果没有启用任何过滤选项，直接返回原始内容
    if (!censorVcp && (typeof maxLength !== 'number' || maxLength <= 0)) {
        if (DEBUG_MODE) {
            logger.info('VCP过滤', '未启用任何过滤选项，返回原始内容');
        }
        return content;
    }

    // 正则表达式匹配 <<<[TOOL_REQUEST]>>> 块，并捕获可选的Markdown代码块包装
    const regex = /(```[a-zA-Z]*\r?\n)?(<<<\[TOOL_REQUEST\]>>>[\s\S]*?<<<\[END_TOOL_REQUEST\]>>>)(\r?\n```)?/g;

    const matches = [...content.matchAll(regex)];
    if (DEBUG_MODE) {
        logger.info('VCP过滤', `找到 ${matches.length} 个工具请求块`);
    }

    const filteredContent = content.replace(regex, (match, startWrapper, toolBlock, endWrapper) => {
        if (DEBUG_MODE) {
            logger.info('VCP过滤', `处理工具块: ${toolBlock.substring(0, 100)}...`);
        }

        // 如果 censor_vcp_output 为 true，则完全删除该块
        if (censorVcp) {
            if (DEBUG_MODE) {
                logger.info('VCP过滤', '完全删除工具块（censor模式）');
            }
            return '';
        }

        // 如果设置了 vcp_content_max_length，则截断内部内容
        if (typeof maxLength === 'number' && maxLength > 0) {
            const startMarker = '<<<[TOOL_REQUEST]>>>';
            const endMarker = '<<<[END_TOOL_REQUEST]>>>';
            const startIndex = toolBlock.indexOf(startMarker) + startMarker.length;
            const endIndex = toolBlock.lastIndexOf(endMarker);
            const innerContent = toolBlock.substring(startIndex, endIndex);

            if (innerContent.length > maxLength) {
                const truncatedInnerContent = innerContent.substring(0, maxLength) + '...';
                // 重构工具块
                const newToolBlock = startMarker + truncatedInnerContent + endMarker;
                // 如果存在，则重新添加包装器
                const result = (startWrapper || '') + newToolBlock + (endWrapper || '');
                
                if (DEBUG_MODE) {
                    logger.info('VCP过滤', `截断工具块内容: ${innerContent.length} -> ${maxLength}`);
                }
                
                return result;
            }
        }

        // 如果不需要截断，则返回原始匹配项
        if (DEBUG_MODE) {
            logger.info('VCP过滤', '工具块无需处理，保持原样');
        }
        return match;
    });

    if (DEBUG_MODE) {
        logger.info('VCP过滤', `过滤完成 - 原长度: ${content.length}, 过滤后长度: ${filteredContent.length}`);
    }

    return filteredContent;
}


app.get('/v1/models', async (req, res) => {
    const { default: fetch } = await import('node-fetch');
    try {
        const modelsApiUrl = `${apiUrl}/v1/models`;
        const apiResponse = await fetch(modelsApiUrl, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                ...(req.headers['user-agent'] && { 'User-Agent': req.headers['user-agent'] }),
                'Accept': req.headers['accept'] || 'application/json',
            },
        });

        // Forward the status code and headers from the upstream API
        res.status(apiResponse.status);
        apiResponse.headers.forEach((value, name) => {
            // Avoid forwarding hop-by-hop headers
            if (!['content-encoding', 'transfer-encoding', 'connection', 'content-length', 'keep-alive'].includes(name.toLowerCase())) {
                 res.setHeader(name, value);
            }
        });

        // Stream the response body back to the client
        apiResponse.body.pipe(res);

    } catch (error) {
        logger.error('模型列表', `转发 /v1/models 请求时出错: ${error.message}`, error.stack);
        if (!res.headersSent) {
             res.status(500).json({ error: 'Internal Server Error', details: error.message });
        } else if (!res.writableEnded) {
             logger.error('流式响应', '响应头已发送。无法发送JSON错误。如果流未结束则结束流');
             res.end();
        }
    }
});

app.post('/v1/chat/completions', async (req, res) => {
    const { default: fetch } = await import('node-fetch');
    try {
        let originalBody = req.body;
        await writeDebugLog('LogInput', originalBody);
        
        // === 新增：在最开始就保存完全原始的用户消息，用于图片渲染 ===
        let originalUserMessageForRender = '';
        if (originalBody.messages && Array.isArray(originalBody.messages)) {
            // 找到最后一个用户消息
            for (let i = originalBody.messages.length - 1; i >= 0; i--) {
                if (originalBody.messages[i].role === 'user') {
                    // 处理字符串类型的content
                    if (typeof originalBody.messages[i].content === 'string') {
                        originalUserMessageForRender = originalBody.messages[i].content;
                    }
                    // 处理数组类型的content（多模态消息）
                    else if (Array.isArray(originalBody.messages[i].content)) {
                        const textParts = originalBody.messages[i].content
                            .filter(part => part.type === 'text')
                            .map(part => part.text || '')
                            .join(' ');
                        originalUserMessageForRender = textParts;
                    }
                    break;
                }
            }
        }
        
        // === 新增：提取对话记录相关参数 ===
        const memoryTracking = originalBody.memory_tracking === true;
        const enableContext = originalBody.enable_context === true;
        const userId = originalBody.userId || originalBody.user_id || 'anonymous';
        const userMessage = originalBody.messages ? 
            originalBody.messages.filter(m => m.role === 'user').pop()?.content || '' : '';
        
        // === 新增：提取VCP过滤参数 ===
        const censorVcpOutput = originalBody.censor_vcp_output === true;
        const vcpContentMaxLength = originalBody.vcp_content_max_length;

        // 移除这行：let originalUserMessageForRender = userMessage; // 保存原始用户消息，用于图片渲染
        
        // === 新增：动态上下文注入（仅VCP模式）===
        let cleanedBody = { ...originalBody };
        if (memoryTracking && enableContext && userId !== 'anonymous' && userMessage && !VCP_USE_OPENAI_TOOLS) {
            try {
                // 获取assistantName用于上下文生成
                const assistantName = originalBody.assistantName || originalBody.assistant_name || 'Assistant';

                // 使用智能情感记忆系统插件获取智能上下文
                if (global.advancedMemoryPlugin) {
                    logger.info('上下文注入', `VCP模式：开始生成智能上下文 [${userId}]`);
                    const contextSummary = await generateIntelligentContext(userId, userMessage, originalBody.maxContextSize || 15, assistantName);

                    // 获取System级心理状态强制指令
                    logger.info('心理状态', `VCP模式：开始生成心理状态指令 [${userId}]`);
                    const psychologyInstructions = await global.advancedMemoryPlugin.generateSystemPsychologyInstructions(userId, assistantName);

                    // 合并上下文和心理状态指令
                    let fullSystemContent = '';
                    if (contextSummary && contextSummary.trim()) {
                        fullSystemContent += contextSummary;
                    }
                    if (psychologyInstructions && psychologyInstructions.trim()) {
                        fullSystemContent += psychologyInstructions;
                        logger.info('心理状态', `VCP模式为用户 ${userId} (${assistantName}) 注入了心理状态指令，长度: ${psychologyInstructions.length}`);

                        // 调试：打印心理状态指令内容
                        //console.log('\n' + '='.repeat(60));
                        //console.log('🧠 心理状态强制指令内容:');
                        //console.log('='.repeat(60));
                        //console.log(psychologyInstructions);
                        //console.log('='.repeat(60) + '\n');
                    } else {
                        logger.info('心理状态', `VCP模式为用户 ${userId} (${assistantName}) 心理状态指令为空（所有状态为默认值）`);
                    }

                    if (fullSystemContent.trim()) {
                        // 查找并更新system消息
                        let hasSystemMessage = false;
                        cleanedBody.messages = cleanedBody.messages.map(msg => {
                            if (msg.role === 'system') {
                                hasSystemMessage = true;
                                return {
                                    ...msg,
                                    content: msg.content + fullSystemContent
                                };
                            }
                            return msg;
                        });

                        // 如果没有system消息，添加一个
                        if (!hasSystemMessage) {
                            cleanedBody.messages.unshift({
                                role: 'system',
                                content: `你是一个智能助手。${fullSystemContent}`
                            });
                        }

                        logger.info('智能上下文', `VCP模式为用户 ${userId} (${assistantName}) 注入了完整system内容，长度: ${fullSystemContent.length}`);

                        // 调试：完整打印system消息内容
                        if (DEBUG_MODE) {
                            //console.log('\n' + '='.repeat(80));
                            //console.log('🔍 VCP模式完整的System消息内容:');
                            //console.log('='.repeat(80));
                            //console.log(fullSystemContent);
                            //console.log('='.repeat(80));
                            //console.log(`📏 System消息长度: ${fullSystemContent.length} 字符\n`);
                        }
                    } else {
                        logger.debug('智能上下文', `VCP模式为用户 ${userId} 生成的system内容为空`);
                    }
                } else {
                    // 回退到原有的ConversationLogger
                    const ConversationLogger = require('./utils/conversationLogger');
                    const contextLogger = new ConversationLogger();
                    
                    const relevantContext = await contextLogger.getRelevantContext(userId, userMessage, {
                        maxContextSize: originalBody.maxContextSize || 15
                    });
                    
                    if (relevantContext && relevantContext.length > 0) {
                        const contextMessage = contextLogger.formatContextForSystem(relevantContext, userMessage, {
                            maxContextSize: originalBody.maxContextSize || 15
                        });
                        
                        let hasSystemMessage = false;
                        cleanedBody.messages = cleanedBody.messages.map(msg => {
                            if (msg.role === 'system') {
                                hasSystemMessage = true;
                                return {
                                    ...msg,
                                    content: msg.content + contextMessage
                                };
                            }
                            return msg;
                        });
                        
                        if (!hasSystemMessage) {
                            cleanedBody.messages.unshift({
                                role: 'system',
                                content: `你是一个智能助手。${contextMessage}`
                            });
                        }
                        
                        logger.info('调试日志', `为用户 ${userId} 注入了 ${relevantContext.length} 条相关上下文（回退模式）`);
                    }
                }
            } catch (contextError) {
                logger.warning('动态上下文', '动态上下文注入失败:', contextError.message);
            }
        }
        
        // === 新增：Agent参数处理 ===
        await processAgentParameter(originalBody, cleanedBody, AGENT_DIR);

        // === 新增：世界树系统增强 ===
        try {
            const worldTreePlugin = pluginManager.serviceModules.get('WorldTree');
            if (worldTreePlugin && worldTreePlugin.module && typeof worldTreePlugin.module.processMessage === 'function') {
                await worldTreePlugin.module.processMessage(originalBody, cleanedBody);
                if (DEBUG_MODE) {
                    logger.info('世界树系统', '已应用世界树增强');
                }
            }
        } catch (worldTreeError) {
            logger.warning('世界树系统', '世界树增强失败:', worldTreeError.message);
        }

        // === 新增：处理userId前缀和本地图片路径解析 ===
        // 获取真正的原始用户消息（不包含任何处理）
        const lastUserMessage = cleanedBody.messages.find(msg => msg.role === 'user');
        let originalUserContent = lastUserMessage ? lastUserMessage.content : '';

        // 如果content是数组格式，提取文本内容
        if (Array.isArray(originalUserContent)) {
            const textContent = originalUserContent.find(item => item.type === 'text');
            originalUserContent = textContent ? textContent.text : '';
        }

        let processedUserMessage = originalUserMessageForRender;

        if (DEBUG_MODE) {
            logger.info('消息处理', `原始用户消息: ${originalUserContent.substring(0, 100)}...`);
            logger.info('消息处理', `渲染用消息: ${processedUserMessage.substring(0, 100)}...`);
        }

        // 使用组件处理用户消息
        processedUserMessage = processUserMessage(processedUserMessage, cleanedBody, DEBUG_MODE);

        // 应用到最后一个用户消息
        if (cleanedBody.messages && Array.isArray(cleanedBody.messages)) {
            const lastUserMessageIndex = cleanedBody.messages.map(msg => msg.role).lastIndexOf('user');
            if (lastUserMessageIndex !== -1) {
                cleanedBody.messages[lastUserMessageIndex].content = processedUserMessage;
            }
        }

        // === 新增：useragent参数处理 ===
        await processUserAgentParameter(originalBody, cleanedBody, AGENT_DIR);

        // === 新增：聊天上下文参数处理 ===
        await processChatContextParameter(originalBody, cleanedBody);

        // === 打印详细的messages数组内容 ===
        //console.log('\n=== 📨 API请求Messages数组 ===');
        //console.log(`时间: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}`);
        //console.log(`用户: ${originalBody.userId || 'N/A'}, 助手: ${originalBody.assistantName || 'N/A'}, 长度: ${cleanedBody.messages.length}`);
        //console.log('Messages JSON:');
        //console.log(JSON.stringify(cleanedBody.messages, null, 2));
        //console.log('=== 📨 Messages数组内容结束 ===\n');

        // 清理请求体，移除自定义参数避免传递给AI API
        cleanRequestBody(cleanedBody);
        
        // === 新增：检查请求模式 ===
        const requestType = originalBody.type || (VCP_USE_OPENAI_TOOLS ? 'mcp' : 'vcp'); // 根据配置决定默认模式
        
        // 如果启用了MCP模式，使用OpenAI工具处理流程
        if (VCP_USE_OPENAI_TOOLS && (requestType === 'mcp' || originalBody.tools)) {
            logger.info('请求处理', '使用MCP模式：OpenAI工具替代VCP判断');
            return await handleMCPRequest(req, res);
        }
        
        // 移除type字段，避免传递给AI API
        if (originalBody.type) {
            delete originalBody.type;
        }
        
        logger.info('请求处理', `使用${requestType.toUpperCase()}模式处理请求`);


        
        // 变量替换已移至发送API前处理
        await writeDebugLog('LogOutputAfterProcessing', cleanedBody);

        // 变量替换现在在这里发生，使用cleanedBody而不是originalBody
        if (cleanedBody.messages && Array.isArray(cleanedBody.messages)) {
            cleanedBody.messages = await processMessagesVariableReplacement(cleanedBody.messages, cleanedBody.model, AGENT_DIR);
        }


        
        let firstAiAPIResponse = await fetch(`${apiUrl}/v1/chat/completions`, {
            method: 'POST',
            headers: { 
                'Content-Type': 'application/json', 
                'Authorization': `Bearer ${apiKey}`, 
                ...(req.headers['user-agent'] && { 'User-Agent': req.headers['user-agent'] }),
                'Accept': cleanedBody.stream ? 'text/event-stream' : (req.headers['accept'] || 'application/json'),
            },
            body: JSON.stringify(cleanedBody),
        });

        const isOriginalResponseStreaming = cleanedBody.stream === true && firstAiAPIResponse.headers.get('content-type')?.includes('text/event-stream');
        
        if (!res.headersSent) {
            res.status(firstAiAPIResponse.status);
            firstAiAPIResponse.headers.forEach((value, name) => {
                if (!['content-encoding', 'transfer-encoding', 'connection', 'content-length', 'keep-alive'].includes(name.toLowerCase())) {
                     res.setHeader(name, value);
                }
            });
            if (isOriginalResponseStreaming && !res.getHeader('Content-Type')?.includes('text/event-stream')) {
                res.setHeader('Content-Type', 'text/event-stream');
                if (!res.getHeader('Cache-Control')) res.setHeader('Cache-Control', 'no-cache');
                if (!res.getHeader('Connection')) res.setHeader('Connection', 'keep-alive');
            }
        }

        let firstResponseRawDataForClientAndDiary = ""; // Used for non-streaming and initial diary

        if (isOriginalResponseStreaming) {
            let currentMessagesForLoop = cleanedBody.messages ? JSON.parse(JSON.stringify(cleanedBody.messages)) : [];
            let recursionDepth = 0;
            const maxRecursion = parseInt(process.env.MaxVCPLoopStream) || 5;
            let currentAIContentForLoop = '';
            let currentAIRawDataForDiary = '';

            // Helper function to process an AI response stream
            async function processAIResponseStreamHelper(aiResponse, isInitialCall) {
                return new Promise((resolve, reject) => {
                    let sseBuffer = ""; // Buffer for incomplete SSE lines
                    let collectedContentThisTurn = ""; // Collects textual content from delta
                    let rawResponseDataThisTurn = ""; // Collects all raw chunks for diary

                    aiResponse.body.on('data', (chunk) => {
                        const chunkString = chunk.toString('utf-8');
                        rawResponseDataThisTurn += chunkString;

                        let isChunkAnEndOfStreamSignal = false;
                        if (chunkString.includes("data: [DONE]")) {
                            isChunkAnEndOfStreamSignal = true;
                        } else {
                            const linesInChunk = chunkString.split('\n');
                            for (const line of linesInChunk) {
                                if (line.startsWith('data: ')) {
                                    const jsonData = line.substring(5).trim();
                                    if (jsonData === '[DONE]') { // Should be caught by the outer check, but good to be safe
                                        isChunkAnEndOfStreamSignal = true;
                                        break;
                                    }
                                    if (jsonData && !jsonData.startsWith("[")) { // Avoid trying to parse "[DONE]" as JSON
                                        try {
                                            const parsedData = JSON.parse(jsonData);
                                            if (parsedData.choices && parsedData.choices[0] && parsedData.choices[0].finish_reason) {
                                                isChunkAnEndOfStreamSignal = true;
                                                break;
                                            }
                                        } catch (e) { /* ignore parse errors, not a relevant JSON structure */ }
                                    }
                                }
                            }
                        }

                        if (!res.writableEnded) {
                            if (isChunkAnEndOfStreamSignal) {
                                // If the chunk is or contains an end-of-stream signal (DONE or finish_reason),
                                // do not forward it directly. The final [DONE] will be sent by the server's main loop.
                                // Its content will still be collected by the sseBuffer logic below.
                            } else {
                                res.write(chunkString);
                            }
                        }
                        
                        // SSE parsing for content collection
                        sseBuffer += chunkString;
                        let lines = sseBuffer.split('\n');
                        sseBuffer = lines.pop(); // Keep incomplete line for the next 'data' event or 'end'

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                const jsonData = line.substring(5).trim();
                                if (jsonData !== '[DONE]' && jsonData) { // Ensure jsonData is not empty and not "[DONE]"
                                    try {
                                        const parsedData = JSON.parse(jsonData);
                                        collectedContentThisTurn += parsedData.choices?.[0]?.delta?.content || '';
                                    } catch (e) { /* ignore parse error for intermediate chunks */ }
                                }
                            }
                        }
                    });

                    aiResponse.body.on('end', () => {
                        // Process remaining sseBuffer for content
                        if (sseBuffer.trim().length > 0) {
                            const finalLines = sseBuffer.split('\n');
                            for (const line of finalLines) {
                                const trimmedLine = line.trim();
                                if (trimmedLine.startsWith('data: ')) {
                                    const jsonData = trimmedLine.substring(5).trim();
                                    if (jsonData !== '[DONE]' && jsonData) { // Ensure jsonData is not empty and not "[DONE]"
                                        try {
                                            const parsedData = JSON.parse(jsonData);
                                            collectedContentThisTurn += parsedData.choices?.[0]?.delta?.content || '';
                                        } catch (e) { /* ignore */ }
                                    }
                                }
                            }
                        }
                        resolve({ content: collectedContentThisTurn, raw: rawResponseDataThisTurn });
                    });
                    aiResponse.body.on('error', (streamError) => {
                        logger.warning('ai反馈失败', "Error reading AI response stream in loop:", streamError);
                        if (!res.writableEnded) {
                            // Try to send an error message before closing if possible
                            try {
                                res.write(`data: ${JSON.stringify({ error: "STREAM_READ_ERROR", message: streamError.message })}\n\n`);
                            } catch (e) { /* ignore if write fails */ }
                            res.end();
                        }
                        reject(streamError);
                    });
                });
            }

            // --- Initial AI Call ---
                            if (DEBUG_MODE) logger.info('VCP流式循环', '处理初始AI调用');
            let initialAIResponseData = await processAIResponseStreamHelper(firstAiAPIResponse, true);
            currentAIContentForLoop = initialAIResponseData.content;
            currentAIRawDataForDiary = initialAIResponseData.raw;
            await handleDiaryFromAIResponse(currentAIRawDataForDiary);
            if (DEBUG_MODE) logger.info('VCP流式循环', `初始AI内容 (前200字符): ${currentAIContentForLoop.substring(0, 200)}`);

            // --- VCP Loop ---
            while (recursionDepth < maxRecursion) {
                currentMessagesForLoop.push({ role: 'assistant', content: currentAIContentForLoop });

                const toolRequestStartMarker = "<<<[TOOL_REQUEST]>>>";
                const toolRequestEndMarker = "<<<[END_TOOL_REQUEST]>>>";
                let toolCallsInThisAIResponse = [];
                let searchOffset = 0;

                while (searchOffset < currentAIContentForLoop.length) {
                    const startIndex = currentAIContentForLoop.indexOf(toolRequestStartMarker, searchOffset);
                    if (startIndex === -1) break;

                    const endIndex = currentAIContentForLoop.indexOf(toolRequestEndMarker, startIndex + toolRequestStartMarker.length);
                    if (endIndex === -1) {
                        if (DEBUG_MODE) logger.warning('VCP流式循环', `找到 TOOL_REQUEST_START 但在偏移量 ${searchOffset} 后没有 END 标记`);
                        searchOffset = startIndex + toolRequestStartMarker.length;
                        continue;
                    }

                    const requestBlockContent = currentAIContentForLoop.substring(startIndex + toolRequestStartMarker.length, endIndex).trim();
                    let parsedToolArgs = {};
                    let requestedToolName = null;
                    const paramRegex = /([\w_]+)\s*:\s*「始」([\s\S]*?)「末」\s*(?:,)?/g;
                    let regexMatch;
                    while ((regexMatch = paramRegex.exec(requestBlockContent)) !== null) {
                        const key = regexMatch[1];
                        const value = regexMatch[2].trim();
                        if (key === "tool_name") requestedToolName = value;
                        else parsedToolArgs[key] = value;
                    }

                    if (requestedToolName) {
                        toolCallsInThisAIResponse.push({ name: requestedToolName, args: parsedToolArgs });
                         if (DEBUG_MODE) logger.info('VCP流式循环', `解析的工具请求: ${requestedToolName}`, parsedToolArgs);
                    } else {
                        if (DEBUG_MODE) logger.warning('VCP流式循环', `解析了工具请求块但未找到 tool_name: ${requestBlockContent.substring(0, 100)}`);
                    }
                    searchOffset = endIndex + toolRequestEndMarker.length;
                }

                if (toolCallsInThisAIResponse.length === 0) {
                    if (DEBUG_MODE) logger.system('[VCP Stream Loop] No tool calls found in AI response. Exiting loop.');
                    break;
                }
                if (DEBUG_MODE) logger.system(`[VCP Stream Loop] Found ${toolCallsInThisAIResponse.length} tool calls. Iteration ${recursionDepth + 1}.`);

                let allToolResultsContentForAI = [];
                const toolExecutionPromises = toolCallsInThisAIResponse.map(async (toolCall) => {
                    let toolResultText;
                    if (pluginManager.getPlugin(toolCall.name)) {
                        try {
                            if (DEBUG_MODE) logger.system(`[VCP Stream Loop] Executing tool: ${toolCall.name} with args:`, toolCall.args);
                            const pluginResult = await pluginManager.processToolCall(toolCall.name, toolCall.args); // pluginResult is the direct output from the plugin's stdout (parsed if JSON)
                            toolResultText = (pluginResult !== undefined && pluginResult !== null) ? (typeof pluginResult === 'object' ? JSON.stringify(pluginResult) : String(pluginResult)) : `插件 ${toolCall.name} 执行完毕，但没有返回明确内容。`;
                            
                            // Push to VCPLog via WebSocketServer (for VCP call logging)
                            webSocketServer.broadcast({
                                type: 'vcp_log',
                                data: { tool_name: toolCall.name, status: 'success', content: toolResultText, source: 'stream_loop' }
                            }, 'VCPLog');

                            // Check manifest for WebSocket push for this plugin's actual result
                            const pluginManifestForStream = pluginManager.getPlugin(toolCall.name);
                            if (pluginManifestForStream && pluginManifestForStream.webSocketPush && pluginManifestForStream.webSocketPush.enabled) {
                                let messageToSend = pluginResult; // By default, use the direct plugin result
                                if (!pluginManifestForStream.webSocketPush.usePluginResultAsMessage && pluginManifestForStream.webSocketPush.messageType) {
                                    // If not using direct result, and a messageType is specified, wrap it
                                    messageToSend = { type: pluginManifestForStream.webSocketPush.messageType, data: pluginResult };
                                }
                                // Ensure messageToSend is an object for broadcast, as AgentMessage.js produces an object.
                                // pluginResult is the raw output from processToolCall
                                const wsPushMessageStream = {
                                    type: pluginManifestForStream.webSocketPush.messageType || `vcp_tool_result_${toolCall.name}`, // Use type from manifest
                                    data: pluginResult // pluginResult is the object from processToolCall
                                };
                                webSocketServer.broadcast(wsPushMessageStream, pluginManifestForStream.webSocketPush.targetClientType || null);
                                if (DEBUG_MODE) logger.info('VCP流式循环WebSocket', `工具 ${toolCall.name} (成功) 推送已处理`, JSON.stringify(wsPushMessageStream, null, 2).substring(0, 500));
                            }

                            if (SHOW_VCP_OUTPUT && !res.writableEnded) { // Still respect SHOW_VCP_OUTPUT for the main client stream
                                const vcpClientPayload = { 
                                    type: 'vcp_stream_result', 
                                    tool_name: toolCall.name, 
                                    status: 'success', 
                                    content: toolResultText,
                                    execution_time: Date.now(),
                                    args: toolCall.args,
                                    metadata: {
                                        plugin_type: 'vcp',
                                        execution_mode: 'stream'
                                    }
                                };
                                res.write(`data: ${JSON.stringify(vcpClientPayload)}\n\n`);
                            }
                        } catch (pluginError) {
                             logger.warning('vcp', `[VCP Stream Loop EXECUTION ERROR] Error executing plugin ${toolCall.name}:`, pluginError.message);
                             toolResultText = `执行插件 ${toolCall.name} 时发生错误：${pluginError.message || '未知错误'}`;
                             // Push error to VCPLog via WebSocketServer
                            webSocketServer.broadcast({
                                type: 'vcp_log',
                                data: { tool_name: toolCall.name, status: 'error', content: toolResultText, source: 'stream_loop_error' }
                            }, 'VCPLog');
                             if (SHOW_VCP_OUTPUT && !res.writableEnded) { // Still respect SHOW_VCP_OUTPUT for the main client stream
                                const vcpClientPayload = { type: 'vcp_stream_result', tool_name: toolCall.name, status: 'error', content: toolResultText };
                                res.write(`data: ${JSON.stringify(vcpClientPayload)}\n\n`);
                             }
                        }
                    } else {
                        toolResultText = `错误：未找到名为 "${toolCall.name}" 的插件。`;
                        if (DEBUG_MODE) console.warn(`[VCP Stream Loop] ${toolResultText}`);
                        // Push not found error to VCPLog via WebSocketServer
                        webSocketServer.broadcast({
                            type: 'vcp_log',
                            data: { tool_name: toolCall.name, status: 'error', content: toolResultText, source: 'stream_loop_not_found' }
                        }, 'VCPLog');
                        if (SHOW_VCP_OUTPUT && !res.writableEnded) { // Still respect SHOW_VCP_OUTPUT for the main client stream
                            const vcpClientPayload = { type: 'vcp_stream_result', tool_name: toolCall.name, status: 'error', content: toolResultText };
                            res.write(`data: ${JSON.stringify(vcpClientPayload)}\n\n`);
                        }
                    }
                    return `来自工具 "${toolCall.name}" 的结果:\n${toolResultText}`;
                });

                allToolResultsContentForAI = await Promise.all(toolExecutionPromises);
                const combinedToolResultsForAI = allToolResultsContentForAI.join("\n\n---\n\n");
                currentMessagesForLoop.push({ role: 'user', content: combinedToolResultsForAI });
                if (DEBUG_MODE) logger.system('[VCP Stream Loop] Combined tool results for next AI call (first 200):', combinedToolResultsForAI.substring(0, 200));

                // --- Make next AI call (stream: true) ---
                if (!res.writableEnded) {
                    res.write('\n'); // 在下一个AI响应开始前，向客户端发送一个换行符
                }
                if (DEBUG_MODE) logger.system('[VCP Stream Loop] Fetching next AI response.');
                const nextAiAPIResponse = await fetch(`${apiUrl}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${apiKey}`,
                        ...(req.headers['user-agent'] && { 'User-Agent': req.headers['user-agent'] }),
                        'Accept': 'text/event-stream', // Ensure streaming for subsequent calls
                    },
                    body: JSON.stringify({ ...cleanedBody, messages: currentMessagesForLoop, stream: true }),
                });

                if (!nextAiAPIResponse.ok) {
                    const errorBodyText = await nextAiAPIResponse.text();
                    logger.warning('vcp', `[VCP Stream Loop] AI call in loop failed (${nextAiAPIResponse.status}): ${errorBodyText}`);
                    if (!res.writableEnded) {
                        try {
                            res.write(`data: ${JSON.stringify({ error: "AI_CALL_FAILED_IN_LOOP", status: nextAiAPIResponse.status, message: errorBodyText })}\n\n`);
                        } catch (e) { /* ignore */ }
                    }
                    break;
                }
                
                // Process the stream from the next AI call
                let nextAIResponseData = await processAIResponseStreamHelper(nextAiAPIResponse, false);
                currentAIContentForLoop = nextAIResponseData.content;
                currentAIRawDataForDiary = nextAIResponseData.raw;
                await handleDiaryFromAIResponse(currentAIRawDataForDiary);
                if (DEBUG_MODE) logger.info('调试日志', '[VCP Stream Loop] Next AI content (first 200):', currentAIContentForLoop.substring(0, 200));
                
                recursionDepth++;
            }

            // After loop (or if no tools called initially / max recursion hit)
            if (!res.writableEnded) {
                if (DEBUG_MODE) logger.info('调试日志', '[VCP Stream Loop] Loop finished. Preparing to send final signals.');

                // === 新增：VCP工具调用对话记录功能（流式） ===
                if (originalBody.memory_tracking) {
                    try {
                        // === 调用智能情感记忆系统记录 ===
                        await recordEmotionMemory(
                            originalBody.userId || originalBody.user_id,
                            originalUserContent,
                            currentAIContentForLoop,
                            originalBody.assistantName || originalBody.assistant_name || 'Assistant',
                            {
                                type: 'vcp_tools',
                                model: cleanedBody.model || 'unknown',
                                ip: req.ip || req.connection.remoteAddress || 'unknown',
                                stream: true,
                                toolCalls: toolCallsInThisAIResponse.length > 0,
                                chatType: 'private', // 默认私聊
                                chatName: '', // 私聊无群名
                                recordingMode: 'stream_vcp_tools',
                                toolsUsed: toolCallsInThisAIResponse.map(t => t.name),
                                recursionDepth: recursionDepth
                            }
                        );

                    } catch (logError) {
                        logger.error('对话记录', `VCP工具调用对话记录失败: ${logError.message}`);
                    }
                }

                let finalFinishReasonToSend = null;
                let lastValidChunkFromAIForMetadata = null;

                // === 新增：应用VCP过滤（流式响应） ===
                let filteredAIContentForRender = currentAIContentForLoop;
                if (censorVcpOutput || (typeof vcpContentMaxLength === 'number' && vcpContentMaxLength > 0)) {
                    filteredAIContentForRender = applyVcpFiltering(currentAIContentForLoop, {
                        censor_vcp_output: censorVcpOutput,
                        vcp_content_max_length: vcpContentMaxLength
                    });
                }

                // 处理图片渲染（流式响应）
                if (originalBody.render_as_image === true) {
                    try {
                        const HtmlToImageRenderer = require('./Puppeteer/HtmlToImageRenderer');
                        const renderer = new HtmlToImageRenderer();
                        
                        // 准备渲染数据
                        const renderData = {
                            model: cleanedBody.model || 'AI助手',
                            id1: originalBody.user_id || originalBody.userId || '10000',
                            name: originalBody.user_name || originalBody.userId || '用户',
                            MSG: cleanedBody.messages[cleanedBody.messages.length - 1].content || '',
                            id2: originalBody.assistant_id || '10001',
                            name1: originalBody.assistant_name || originalBody.assistantName || 'AI助手',
                            CONTENT: filteredAIContentForRender, // 使用过滤后的内容
                            theme: originalBody.theme || 'dark'
                        };

                        // 渲染图片
                        const imageUrl = await renderer.renderToImage(renderData);
                        
                        // 发送图片URL到客户端
                        const imagePayload = {
                            type: 'image_url',
                            url: `http://${req.headers.host}${imageUrl}`
                        };
                        res.write(`data: ${JSON.stringify(imagePayload)}\n\n`);
                        
                        // 关闭浏览器实例
                        await renderer.close();
                        
                        logger.info('图片渲染', `成功渲染对话图片: ${imageUrl}`);
                    } catch (renderError) {
                        logger.error('图片渲染', `渲染对话图片失败: ${renderError.message}`);
                        const errorPayload = {
                            type: 'image_error',
                            error: renderError.message
                        };
                        res.write(`data: ${JSON.stringify(errorPayload)}\n\n`);
                    }
                }

                // Attempt to parse the last valid data chunk from the AI's raw output
                // to extract finish_reason and other metadata for the final signal.
                if (currentAIRawDataForDiary) { // currentAIRawDataForDiary holds the last AI response's raw data
                    const lines = currentAIRawDataForDiary.trim().split('\n');
                    for (let i = lines.length - 1; i >= 0; i--) {
                        const line = lines[i];
                        if (line.startsWith('data: ')) {
                            const jsonData = line.substring(5).trim();
                            if (jsonData && jsonData !== '[DONE]' && !jsonData.startsWith("[")) { // Avoid parsing "[DONE]" or non-JSON
                                try {
                                    const parsed = JSON.parse(jsonData);
                                    lastValidChunkFromAIForMetadata = parsed; // Store the last successfully parsed chunk for metadata
                                    if (parsed.choices && parsed.choices[0] && parsed.choices[0].finish_reason) {
                                        finalFinishReasonToSend = parsed.choices[0].finish_reason;
                                    }
                                    break; // Processed the last relevant data line from AI
                                } catch (e) { /* ignore parse error, try previous line if any */ }
                            }
                        }
                    }
                }

                // Determine the finish_reason to send if not extracted from AI's last message
                if (!finalFinishReasonToSend) {
                    if (recursionDepth >= maxRecursion) {
                        finalFinishReasonToSend = 'length';
                    } else {
                        // If loop broke because no tools were called (toolCallsInThisAIResponse.length === 0),
                        // it's considered a natural stop.
                        finalFinishReasonToSend = 'stop';
                    }
                }
                
                // Construct and send the final chunk with finish_reason
                const finalChunkPayload = {
                    id: lastValidChunkFromAIForMetadata?.id || `chatcmpl-VCP-final-${Date.now()}`,
                    object: lastValidChunkFromAIForMetadata?.object || "chat.completion.chunk",
                    created: lastValidChunkFromAIForMetadata?.created || Math.floor(Date.now() / 1000),
                    model: lastValidChunkFromAIForMetadata?.model || cleanedBody.model || "unknown-model", // Use model from original request as fallback
                    choices: [{
                        index: 0,
                        delta: {}, // Delta is typically empty for the chunk that only carries finish_reason
                        finish_reason: finalFinishReasonToSend
                    }]
                };
                res.write(`data: ${JSON.stringify(finalChunkPayload)}\n\n`);
                if (DEBUG_MODE) logger.info('调试日志', '[VCP Stream Loop] Sent final chunk with finish_reason:', finalFinishReasonToSend, 'Payload:', JSON.stringify(finalChunkPayload));

                // Always send [DONE] as well, for clients that rely on it
                res.write('data: [DONE]\n\n');
                if (DEBUG_MODE) logger.info('调试日志', '[VCP Stream Loop] Sent final [DONE].');
                
                // === 新增：流式对话记录功能 ===
                if (memoryTracking) {
                    try {
                        // === 调用智能情感记忆系统记录 ===
                        await recordEmotionMemory(
                            userId,
                            originalUserContent,
                            filteredAIContentForRender || 'Stream response content',
                            originalBody.assistantName || originalBody.assistant_name || 'Assistant',
                            {
                                type: requestType,
                                model: cleanedBody.model || 'unknown',
                                ip: req.ip || req.connection.remoteAddress || 'unknown',
                                stream: true,
                                toolCalls: recursionDepth > 0,
                                chatType: 'private', // 默认私聊
                                chatName: '', // 私聊无群名
                                recordingMode: 'stream_vcp',
                                recursionDepth: recursionDepth,
                                finishReason: finalFinishReasonToSend,
                                vcpFiltered: censorVcpOutput || (typeof vcpContentMaxLength === 'number' && vcpContentMaxLength > 0)
                            }
                        );

                    } catch (logError) {
                        logger.error('对话记录', `记录流式对话失败: ${logError.message}`);
                    }
                }
                
                res.end();
            }

        } else { // Non-streaming (originalBody.stream === false)
            const firstArrayBuffer = await firstAiAPIResponse.arrayBuffer();
            const responseBuffer = Buffer.from(firstArrayBuffer);
            const aiResponseText = responseBuffer.toString('utf-8');
            // firstResponseRawDataForClientAndDiary is used by the non-streaming logic later
            firstResponseRawDataForClientAndDiary = aiResponseText;

            let fullContentFromAI = ''; // This will be populated by the non-streaming logic
            try {
                const parsedJson = JSON.parse(aiResponseText);
                fullContentFromAI = parsedJson.choices?.[0]?.message?.content || '';
            } catch (e) {
                if (DEBUG_MODE) console.warn('[PluginCall] First AI response (non-stream) not valid JSON. Raw:', aiResponseText.substring(0, 200));
                fullContentFromAI = aiResponseText; // Use raw text if not JSON
            }
            
            // --- Non-streaming VCP Loop ---
            let recursionDepth = 0;
            const maxRecursion = parseInt(process.env.MaxVCPLoopNonStream) || 5;
            let conversationHistoryForClient = []; // To build the final response for client
            let currentAIContentForLoop = fullContentFromAI; // Start with the first AI's response content
            let currentMessagesForNonStreamLoop = cleanedBody.messages ? JSON.parse(JSON.stringify(cleanedBody.messages)) : [];
            // `firstResponseRawDataForClientAndDiary` holds the raw first AI response for diary purposes.
            // Subsequent raw AI responses in the non-stream loop will also need diary handling.
            let accumulatedRawResponseDataForDiary = firstResponseRawDataForClientAndDiary;

            do {
                let anyToolProcessedInCurrentIteration = false; // Reset for each iteration of the outer AI-Tool-AI loop
                // Add the *current* AI content to the client history *before* processing it for tools
                conversationHistoryForClient.push({ type: 'ai', content: currentAIContentForLoop });

                const toolRequestStartMarker = "<<<[TOOL_REQUEST]>>>";
                const toolRequestEndMarker = "<<<[END_TOOL_REQUEST]>>>";
                let toolCallsInThisAIResponse = []; // Stores {name, args} for each tool call found in currentAIContentForLoop
                
                let searchOffset = 0;
                while (searchOffset < currentAIContentForLoop.length) {
                    const startIndex = currentAIContentForLoop.indexOf(toolRequestStartMarker, searchOffset);
                    if (startIndex === -1) break; // No more start markers

                    const endIndex = currentAIContentForLoop.indexOf(toolRequestEndMarker, startIndex + toolRequestStartMarker.length);
                    if (endIndex === -1) {
                        if (DEBUG_MODE) console.warn("[Multi-Tool] Found TOOL_REQUEST_START but no END marker after offset", searchOffset);
                        searchOffset = startIndex + toolRequestStartMarker.length; // Skip malformed start
                        continue;
                    }

                    const requestBlockContent = currentAIContentForLoop.substring(startIndex + toolRequestStartMarker.length, endIndex).trim();
                    let parsedToolArgs = {};
                    let requestedToolName = null;
                    const paramRegex = /([\w_]+)\s*:\s*「始」([\s\S]*?)「末」\s*(?:,)?/g;
                    let regexMatch;
                    while ((regexMatch = paramRegex.exec(requestBlockContent)) !== null) {
                        const key = regexMatch[1];
                        const value = regexMatch[2].trim();
                        if (key === "tool_name") requestedToolName = value;
                        else parsedToolArgs[key] = value;
                    }

                    if (requestedToolName) {
                        toolCallsInThisAIResponse.push({ name: requestedToolName, args: parsedToolArgs });
                    } else {
                        if (DEBUG_MODE) console.warn("[Multi-Tool] Parsed a tool request block but no tool_name found:", requestBlockContent);
                    }
                    searchOffset = endIndex + toolRequestEndMarker.length; // Move past the processed block
                }

                if (toolCallsInThisAIResponse.length > 0) {
                    anyToolProcessedInCurrentIteration = true; // At least one tool request was found in the AI's response
                    let allToolResultsContentForAI = [];

                    // Add the AI's full response (that contained the tool requests) to the messages for the next AI call
                    currentMessagesForNonStreamLoop.push({ role: 'assistant', content: currentAIContentForLoop });

                    // Use Promise.all to execute tool calls potentially in parallel, though JS is single-threaded
                    // The main benefit here is cleaner async/await handling for multiple calls.
                    const toolExecutionPromises = toolCallsInThisAIResponse.map(async (toolCall) => {
                        let toolResultText;
                        if (pluginManager.getPlugin(toolCall.name)) {
                            try {
                                if (DEBUG_MODE) logger.info('调试日志', `[Multi-Tool] Executing tool: ${toolCall.name} with args:`, toolCall.args);
                                const pluginResult = await pluginManager.processToolCall(toolCall.name, toolCall.args); // pluginResult is the direct output
                                toolResultText = (pluginResult !== undefined && pluginResult !== null) ? (typeof pluginResult === 'object' ? JSON.stringify(pluginResult) : String(pluginResult)) : `插件 ${toolCall.name} 执行完毕，但没有返回明确内容。`;
                                
                                // Push to VCPLog via WebSocketServer (for VCP call logging)
                               webSocketServer.broadcast({
                                   type: 'vcp_log',
                                   data: { tool_name: toolCall.name, status: 'success', content: toolResultText, source: 'non_stream_loop' }
                               }, 'VCPLog');

                                // Check manifest for WebSocket push for this plugin's actual result
                                const pluginManifestNonStream = pluginManager.getPlugin(toolCall.name);
                                if (pluginManifestNonStream && pluginManifestNonStream.webSocketPush && pluginManifestNonStream.webSocketPush.enabled) {
                                    let messageToSend = pluginResult; // By default, use the direct plugin result
                                    if (!pluginManifestNonStream.webSocketPush.usePluginResultAsMessage && pluginManifestNonStream.webSocketPush.messageType) {
                                        // If not using direct result, and a messageType is specified, wrap it
                                        messageToSend = { type: pluginManifestNonStream.webSocketPush.messageType, data: pluginResult };
                                    }
                                    // Ensure messageToSend is an object for broadcast
                                    // pluginResult is the raw output from processToolCall
                                    const wsPushMessageNonStream = {
                                        type: pluginManifestNonStream.webSocketPush.messageType || `vcp_tool_result_${toolCall.name}`, // Use type from manifest
                                        data: pluginResult // pluginResult is the object from processToolCall
                                    };
                                    webSocketServer.broadcast(wsPushMessageNonStream, pluginManifestNonStream.webSocketPush.targetClientType || null);
                                    if (DEBUG_MODE) logger.info('调试日志', `[Multi-Tool] WebSocket push for ${toolCall.name} (success) processed. Message:`, JSON.stringify(wsPushMessageNonStream, null, 2).substring(0, 500));
                                }

                                if (SHOW_VCP_OUTPUT) { // Still respect SHOW_VCP_OUTPUT for adding to client's direct response
                                    conversationHistoryForClient.push({ type: 'vcp', content: `工具 ${toolCall.name} 调用结果:\n${toolResultText}` });
                                }
                            } catch (pluginError) {
                                 logger.warning('错误', `[Multi-Tool EXECUTION ERROR] Error executing plugin ${toolCall.name}:`, pluginError.message);
                                 toolResultText = `执行插件 ${toolCall.name} 时发生错误：${pluginError.message || '未知错误'}`;
                                 // Push error to VCPLog via WebSocketServer
                                webSocketServer.broadcast({
                                    type: 'vcp_log',
                                    data: { tool_name: toolCall.name, status: 'error', content: toolResultText, source: 'non_stream_loop_error' }
                                }, 'VCPLog');
                                 if (SHOW_VCP_OUTPUT) { // Still respect SHOW_VCP_OUTPUT for adding to client's direct response
                                    conversationHistoryForClient.push({ type: 'vcp', content: `工具 ${toolCall.name} 调用错误:\n${toolResultText}` });
                                 }
                            }
                        } else {
                            toolResultText = `错误：未找到名为 "${toolCall.name}" 的插件。`;
                            if (DEBUG_MODE) console.warn(`[Multi-Tool] ${toolResultText}`);
                            // Push not found error to VCPLog via WebSocketServer
                           webSocketServer.broadcast({
                               type: 'vcp_log',
                               data: { tool_name: toolCall.name, status: 'error', content: toolResultText, source: 'non_stream_loop_not_found' }
                           }, 'VCPLog');
                            if (SHOW_VCP_OUTPUT) { // Still respect SHOW_VCP_OUTPUT for adding to client's direct response
                                conversationHistoryForClient.push({ type: 'vcp', content: toolResultText });
                            }
                        }
                        return `来自工具 "${toolCall.name}" 的结果:\n${toolResultText}`;
                    });

                    // Wait for all tool executions to complete
                    allToolResultsContentForAI = await Promise.all(toolExecutionPromises);

                    const combinedToolResultsForAI = allToolResultsContentForAI.join("\n\n---\n\n");
                    currentMessagesForNonStreamLoop.push({ role: 'user', content: combinedToolResultsForAI });

                    // Fetch the next AI response
                    if (DEBUG_MODE) logger.info('调试日志', "[Multi-Tool] Fetching next AI response after processing tools.");
                    const recursionAiResponse = await fetch(`${apiUrl}/v1/chat/completions`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${apiKey}`,
                            ...(req.headers['user-agent'] && { 'User-Agent': req.headers['user-agent'] }),
                            'Accept': 'application/json',
                        },
                        body: JSON.stringify({ ...cleanedBody, messages: currentMessagesForNonStreamLoop, stream: false }),
                    });
                    const recursionArrayBuffer = await recursionAiResponse.arrayBuffer();
                    const recursionBuffer = Buffer.from(recursionArrayBuffer);
                    const recursionText = recursionBuffer.toString('utf-8');
                    // Consider appending recursionText to rawResponseDataForDiary if needed for multi-tool turn

                    try {
                        const recursionJson = JSON.parse(recursionText);
                        currentAIContentForLoop = "\n" + (recursionJson.choices?.[0]?.message?.content || '');
                    } catch (e) {
                        currentAIContentForLoop = "\n" + recursionText;
                    }
                } else {
                    // No tool calls found in the currentAIContentForLoop, so this is the final AI response.
                    anyToolProcessedInCurrentIteration = false;
                }
                
                // Exit the outer loop if no tools were processed in this iteration
                if (!anyToolProcessedInCurrentIteration) break;
                recursionDepth++;
            } while (recursionDepth < maxRecursion);

            // --- Finalize Non-Streaming Response ---
            const finalContentForClient = conversationHistoryForClient
                .map(item => {
                    if (item.type === 'ai') return item.content;
                    // VCP results are only included if SHOW_VCP_OUTPUT was true when they were added
                    if (item.type === 'vcp') return `\n<<<[VCP_RESULT]>>>\n${item.content}\n<<<[END_VCP_RESULT]>>>\n`;
                    return '';
                }).join('');

            let finalJsonResponse;
            try {
                // Try to reuse the structure of the *first* AI response
                finalJsonResponse = JSON.parse(aiResponseText);
                 if (!finalJsonResponse.choices || !Array.isArray(finalJsonResponse.choices) || finalJsonResponse.choices.length === 0) {
                    finalJsonResponse.choices = [{ message: {} }];
                }
                if (!finalJsonResponse.choices[0].message) {
                    finalJsonResponse.choices[0].message = {};
                }
                // Overwrite the content with the full conversation history
                finalJsonResponse.choices[0].message.content = finalContentForClient;
                // Optionally update finish_reason if needed, e.g., if maxRecursion was hit
                if (recursionDepth >= maxRecursion) {
                     finalJsonResponse.choices[0].finish_reason = 'length'; // Or 'tool_calls' if appropriate
                } else {
                     finalJsonResponse.choices[0].finish_reason = 'stop'; // Assume normal stop if loop finished early
                }

            } catch (e) {
                // Fallback if the first response wasn't valid JSON
                finalJsonResponse = { choices: [{ index: 0, message: { role: 'assistant', content: finalContentForClient }, finish_reason: (recursionDepth >= maxRecursion ? 'length' : 'stop') }] };
            }

            if (!res.writableEnded) {
                 res.send(Buffer.from(JSON.stringify(finalJsonResponse)));
            }
            // Handle diary for the *first* AI response in non-streaming mode
            await handleDiaryFromAIResponse(firstResponseRawDataForClientAndDiary);

            // Loop for subsequent tool calls and AI responses in non-streaming mode
            do {
                let anyToolProcessedInCurrentIteration = false;
                conversationHistoryForClient.push({ type: 'ai', content: currentAIContentForLoop });

                const toolRequestStartMarker = "<<<[TOOL_REQUEST]>>>";
                const toolRequestEndMarker = "<<<[END_TOOL_REQUEST]>>>";
                let toolCallsInThisAIResponse = [];
                let searchOffset = 0;

                while (searchOffset < currentAIContentForLoop.length) {
                    const startIndex = currentAIContentForLoop.indexOf(toolRequestStartMarker, searchOffset);
                    if (startIndex === -1) break;
                    const endIndex = currentAIContentForLoop.indexOf(toolRequestEndMarker, startIndex + toolRequestStartMarker.length);
                    if (endIndex === -1) {
                        if (DEBUG_MODE) console.warn("[VCP NonStream Loop] Found TOOL_REQUEST_START but no END marker after offset", searchOffset);
                        searchOffset = startIndex + toolRequestStartMarker.length;
                        continue;
                    }
                    const requestBlockContent = currentAIContentForLoop.substring(startIndex + toolRequestStartMarker.length, endIndex).trim();
                    let parsedToolArgs = {};
                    let requestedToolName = null;
                    const paramRegex = /([\w_]+)\s*:\s*「始」([\s\S]*?)「末」\s*(?:,)?/g;
                    let regexMatch;
                    while ((regexMatch = paramRegex.exec(requestBlockContent)) !== null) {
                        const key = regexMatch[1];
                        const value = regexMatch[2].trim();
                        if (key === "tool_name") requestedToolName = value;
                        else parsedToolArgs[key] = value;
                    }
                    if (requestedToolName) {
                        toolCallsInThisAIResponse.push({ name: requestedToolName, args: parsedToolArgs });
                        if (DEBUG_MODE) logger.info('调试日志', `[VCP NonStream Loop] Parsed tool request: ${requestedToolName}`, parsedToolArgs);
                    } else {
                        if (DEBUG_MODE) console.warn("[VCP NonStream Loop] Parsed a tool request block but no tool_name found:", requestBlockContent.substring(0, 100));
                    }
                    searchOffset = endIndex + toolRequestEndMarker.length;
                }

                if (toolCallsInThisAIResponse.length > 0) {
                    anyToolProcessedInCurrentIteration = true;
                    if (DEBUG_MODE) logger.info('调试日志', `[VCP NonStream Loop] Found ${toolCallsInThisAIResponse.length} tool calls. Iteration ${recursionDepth + 1}.`);
                    currentMessagesForNonStreamLoop.push({ role: 'assistant', content: currentAIContentForLoop });
                    
                    let allToolResultsContentForAI = [];
                    const toolExecutionPromises = toolCallsInThisAIResponse.map(async (toolCall) => {
                        let toolResultText;
                        if (pluginManager.getPlugin(toolCall.name)) {
                            try {
                                if (DEBUG_MODE) logger.info('调试日志', `[VCP NonStream Loop] Executing tool: ${toolCall.name} with args:`, toolCall.args);
                                const pluginResult = await pluginManager.processToolCall(toolCall.name, toolCall.args); // pluginResult is the direct output
                                toolResultText = (pluginResult !== undefined && pluginResult !== null) ? (typeof pluginResult === 'object' ? JSON.stringify(pluginResult) : String(pluginResult)) : `插件 ${toolCall.name} 执行完毕，但没有返回明确内容。`;
                                
                                // Push to VCPLog via WebSocketServer (for VCP call logging)
                               webSocketServer.broadcast({
                                   type: 'vcp_log',
                                   data: { tool_name: toolCall.name, status: 'success', content: toolResultText, source: 'non_stream_final_loop' }
                               }, 'VCPLog');

                               // Check manifest for WebSocket push for this plugin's actual result
                                const pluginManifestNonStreamLoop2 = pluginManager.getPlugin(toolCall.name);
                                if (pluginManifestNonStreamLoop2 && pluginManifestNonStreamLoop2.webSocketPush && pluginManifestNonStreamLoop2.webSocketPush.enabled) {
                                    let messageToSend = pluginResult; // By default, use the direct plugin result
                                    if (!pluginManifestNonStreamLoop2.webSocketPush.usePluginResultAsMessage && pluginManifestNonStreamLoop2.webSocketPush.messageType) {
                                        // If not using direct result, and a messageType is specified, wrap it
                                        messageToSend = { type: pluginManifestNonStreamLoop2.webSocketPush.messageType, data: pluginResult };
                                    }
                                    // Ensure messageToSend is an object for broadcast
                                    // pluginResult is the raw output from processToolCall
                                     const wsPushMessageNonStreamLoop = {
                                        type: pluginManifestNonStreamLoop2.webSocketPush.messageType || `vcp_tool_result_${toolCall.name}`, // Use type from manifest
                                        data: pluginResult // pluginResult is the object from processToolCall
                                    };
                                    webSocketServer.broadcast(wsPushMessageNonStreamLoop, pluginManifestNonStreamLoop2.webSocketPush.targetClientType || null);
                                    if (DEBUG_MODE) logger.info('调试日志', `[VCP NonStream Loop] WebSocket push for ${toolCall.name} (success) processed. Message:`, JSON.stringify(wsPushMessageNonStreamLoop, null, 2).substring(0, 500));
                                }

                                if (SHOW_VCP_OUTPUT) {
                                    conversationHistoryForClient.push({ type: 'vcp', content: `工具 ${toolCall.name} 调用结果:\n${toolResultText}` });
                                }
                            } catch (pluginError) {
                                 logger.warning('vcp', `[VCP NonStream Loop EXECUTION ERROR] Error executing plugin ${toolCall.name}:`, pluginError.message);
                                 toolResultText = `执行插件 ${toolCall.name} 时发生错误：${pluginError.message || '未知错误'}`;
                                // Push error to VCPLog via WebSocketServer
                               webSocketServer.broadcast({
                                   type: 'vcp_log',
                                   data: { tool_name: toolCall.name, status: 'error', content: toolResultText, source: 'non_stream_final_loop_error' }
                               }, 'VCPLog');
                                 if (SHOW_VCP_OUTPUT) {
                                    conversationHistoryForClient.push({ type: 'vcp', content: `工具 ${toolCall.name} 调用错误:\n${toolResultText}` });
                                 }
                            }
                        } else {
                            toolResultText = `错误：未找到名为 "${toolCall.name}" 的插件。`;
                            if (DEBUG_MODE) console.warn(`[VCP NonStream Loop] ${toolResultText}`);
                            // Push not found error to VCPLog via WebSocketServer
                           webSocketServer.broadcast({
                               type: 'vcp_log',
                               data: { tool_name: toolCall.name, status: 'error', content: toolResultText, source: 'non_stream_final_loop_not_found' }
                           }, 'VCPLog');
                            if (SHOW_VCP_OUTPUT) {
                                conversationHistoryForClient.push({ type: 'vcp', content: toolResultText });
                            }
                        }
                        return `来自工具 "${toolCall.name}" 的结果:\n${toolResultText}`;
                    });

                    allToolResultsContentForAI = await Promise.all(toolExecutionPromises);
                    const combinedToolResultsForAI = allToolResultsContentForAI.join("\n\n---\n\n");
                    currentMessagesForNonStreamLoop.push({ role: 'user', content: combinedToolResultsForAI });
                    if (DEBUG_MODE) logger.info('调试日志', '[VCP NonStream Loop] Combined tool results for next AI call (first 200):', combinedToolResultsForAI.substring(0, 200));

                    console.log(currentMessagesForNonStreamLoop)
                    if (DEBUG_MODE) logger.info('调试日志', "[VCP NonStream Loop] Fetching next AI response after processing tools.");
                    const recursionAiResponse = await fetch(`${apiUrl}/v1/chat/completions`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${apiKey}`,
                            ...(req.headers['user-agent'] && { 'User-Agent': req.headers['user-agent'] }),
                            'Accept': 'application/json', // Non-streaming for subsequent calls in this loop
                        },
                        body: JSON.stringify({ ...cleanedBody, messages: currentMessagesForNonStreamLoop, stream: false }),
                    });

                    const recursionArrayBuffer = await recursionAiResponse.arrayBuffer();
                    const recursionBuffer = Buffer.from(recursionArrayBuffer);
                    const recursionText = recursionBuffer.toString('utf-8');
                    
                    // Handle diary for this AI response
                    await handleDiaryFromAIResponse(recursionText);
                    accumulatedRawResponseDataForDiary += "\n\n--- Next AI Turn (Non-Stream) ---\n\n" + recursionText;


                    try {
                        const recursionJson = JSON.parse(recursionText);
                        currentAIContentForLoop = "\n" + (recursionJson.choices?.[0]?.message?.content || '');
                    } catch (e) {
                        currentAIContentForLoop = "\n" + recursionText;
                    }
                    if (DEBUG_MODE) logger.info('调试日志', '[VCP NonStream Loop] Next AI content (first 200):', currentAIContentForLoop.substring(0, 200));

                } else {
                    if (DEBUG_MODE) logger.info('调试日志', '[VCP NonStream Loop] No tool calls found in AI response. Exiting loop.');
                    anyToolProcessedInCurrentIteration = false;
                }
                
                if (!anyToolProcessedInCurrentIteration) break;
                recursionDepth++;
            } while (recursionDepth < maxRecursion);

            // Rename variables to avoid redeclaration errors
            const finalContentForClient_nonStream = conversationHistoryForClient
                .map(item => {
                    if (item.type === 'ai') return item.content;
                    if (item.type === 'vcp' && SHOW_VCP_OUTPUT) return `\n<<<[VCP_RESULT]>>>\n${item.content}\n<<<[END_VCP_RESULT]>>>\n`;
                    return '';
                }).join('') + '\n'; // Add newline at the end

            let finalJsonResponse_nonStream;
            try {
                finalJsonResponse_nonStream = JSON.parse(firstResponseRawDataForClientAndDiary); // Try to use structure of first response
                 if (!finalJsonResponse_nonStream.choices || !Array.isArray(finalJsonResponse_nonStream.choices) || finalJsonResponse_nonStream.choices.length === 0) {
                    finalJsonResponse_nonStream.choices = [{ message: {} }];
                }
                if (!finalJsonResponse_nonStream.choices[0].message) {
                    finalJsonResponse_nonStream.choices[0].message = {};
                }
                finalJsonResponse_nonStream.choices[0].message.content = finalContentForClient_nonStream; // Use renamed variable
                finalJsonResponse_nonStream.choices[0].finish_reason = (recursionDepth >= maxRecursion && toolCallsInThisAIResponse.length > 0) ? 'length' : 'stop';

                // 处理图片渲染
                if (originalBody.render_as_image === true && !cleanedBody.stream) {
                    try {
                        const HtmlToImageRenderer = require('./Puppeteer/HtmlToImageRenderer');
                        const renderer = new HtmlToImageRenderer();
                        
                        // 在应用过滤之前获取内容
                        const originalContentForRender = finalContentForClient_nonStream;
                        // 应用过滤和截断
                        const filteredContentForResponse = applyVcpFiltering(originalContentForRender, originalBody);
                        finalJsonResponse_nonStream.choices[0].message.content = filteredContentForResponse;


                        // 准备渲染数据
                        const renderData = {
                            model: cleanedBody.model || 'AI助手',
                            id1: originalBody.user_id || originalBody.userId || '10000', // 保留原始用户ID
                            name: originalBody.user_name || originalBody.userId || '用户',
                            MSG: originalUserMessageForRender || '', // 使用保存的原始消息
                            id2: originalBody.assistant_id || '10001', // 默认助手ID
                            name1: originalBody.assistant_name || originalBody.assistantName || 'AI助手',
                            CONTENT: finalJsonResponse_nonStream.choices[0].message.content, // 使用过滤后的内容进行渲染
                            theme: originalBody.theme || 'dark'
                        };



                        // 渲染图片
                        const imageUrl = await renderer.renderToImage(renderData);
                        
                        // 添加图片URL到响应
                        finalJsonResponse_nonStream.image_url = `http://${req.headers.host}${imageUrl}`;
                        
                        // 关闭浏览器实例
                        await renderer.close();
                    } catch (renderError) {
                        logger.error('图片渲染', `渲染对话图片失败: ${renderError.message}`);
                        finalJsonResponse_nonStream.image_error = renderError.message;
                    }
                } else {
                     // 如果不渲染图片，也应用过滤
                    const filteredContent = applyVcpFiltering(finalContentForClient_nonStream, {
                        censor_vcp_output: censorVcpOutput,
                        vcp_content_max_length: vcpContentMaxLength
                    });
                    finalJsonResponse_nonStream.choices[0].message.content = filteredContent;
                }
            } catch (e) {
                 // Use renamed variables
                const filteredContent = applyVcpFiltering(finalContentForClient_nonStream, {
                    censor_vcp_output: censorVcpOutput,
                    vcp_content_max_length: vcpContentMaxLength
                });
                finalJsonResponse_nonStream = { choices: [{ index: 0, message: { role: 'assistant', content: filteredContent }, finish_reason: (recursionDepth >= maxRecursion && toolCallsInThisAIResponse.length > 0 ? 'length' : 'stop') }] };
            }

            // === 新增：对话记录功能 ===
            if (memoryTracking) {
                try {
                    // === 调用智能情感记忆系统记录 ===
                    await recordEmotionMemory(
                        userId,
                        originalUserContent,
                        finalContentForClient_nonStream,
                        originalBody.assistantName || originalBody.assistant_name || 'Assistant',
                        {
                            type: requestType,
                            model: cleanedBody.model || 'unknown',
                            ip: req.ip || req.connection.remoteAddress || 'unknown',
                            stream: false,
                            toolCalls: recursionDepth > 0,
                            chatType: 'private', // 默认私聊
                            chatName: '', // 私聊无群名
                            recordingMode: 'nonstream_vcp',
                            recursionDepth: recursionDepth,
                            imageUrl: finalJsonResponse_nonStream.image_url || null
                        }
                    );

                } catch (logError) {
                    logger.error('对话记录', `记录对话失败: ${logError.message}`);
                }
            }

            if (!res.writableEnded) {
                 res.send(Buffer.from(JSON.stringify(finalJsonResponse_nonStream))); // Use renamed variable
            }
            // Diary for all turns in non-streaming already handled inside the loop for subsequent, and outside for first.
        }
    } catch (error) {
        logger.error('请求处理', `处理请求或转发时出错: ${error.message}`, error.stack);
        if (!res.headersSent) {
             res.status(500).json({ error: 'Internal Server Error', details: error.message });
        } else if (!res.writableEnded) {
             logger.error('流式响应', '响应头已发送。无法发送JSON错误。如果流未结束则结束流');
             res.end();
        }
    }
});

async function handleDiaryFromAIResponse(responseText) {
    let fullAiResponseTextForDiary = '';
    let successfullyParsedForDiary = false;
    if (!responseText || typeof responseText !== 'string' || responseText.trim() === "") {
        return;
    }
    const lines = responseText.trim().split('\n');
    const looksLikeSSEForDiary = lines.some(line => line.startsWith('data: '));
    if (looksLikeSSEForDiary) {
        let sseContent = '';
        for (const line of lines) {
            if (line.startsWith('data: ')) {
                const jsonData = line.substring(5).trim();
                if (jsonData === '[DONE]') continue;
                try {
                    const parsedData = JSON.parse(jsonData);
                    const contentChunk = parsedData.choices?.[0]?.delta?.content || parsedData.choices?.[0]?.message?.content || '';
                    if (contentChunk) sseContent += contentChunk;
                } catch (e) { /* ignore */ }
            }
        }
        if (sseContent) {
            fullAiResponseTextForDiary = sseContent;
            successfullyParsedForDiary = true;
        }
    }
    if (!successfullyParsedForDiary) { 
        try {
            const parsedJson = JSON.parse(responseText); 
            const jsonContent = parsedJson.choices?.[0]?.message?.content;
            if (jsonContent && typeof jsonContent === 'string') {
                fullAiResponseTextForDiary = jsonContent;
                successfullyParsedForDiary = true;
            }
        } catch (e) { /* ignore */ }
    }
    if (!successfullyParsedForDiary && !looksLikeSSEForDiary) { 
        fullAiResponseTextForDiary = responseText;
    }

    if (fullAiResponseTextForDiary.trim()) {
        const dailyNoteRegex = /<<<DailyNoteStart>>>(.*?)<<<DailyNoteEnd>>>/s;
        const match = fullAiResponseTextForDiary.match(dailyNoteRegex);
        if (match && match[1]) {
            const noteBlockContent = match[1].trim();
            if (DEBUG_MODE) logger.info('日记处理', '发现结构化日记块');

            // Extract Maid, Date, Content from noteBlockContent
            const lines = noteBlockContent.trim().split('\n');
            let maidName = null;
            let dateString = null;
            let contentLines = [];
            let isContentSection = false;

            for (const line of lines) {
                const trimmedLine = line.trim();
                if (trimmedLine.startsWith('Maid:')) {
                    maidName = trimmedLine.substring(5).trim();
                    isContentSection = false;
                } else if (trimmedLine.startsWith('Date:')) {
                    dateString = trimmedLine.substring(5).trim();
                    isContentSection = false;
                } else if (trimmedLine.startsWith('Content:')) {
                    isContentSection = true;
                    const firstContentPart = trimmedLine.substring(8).trim();
                    if (firstContentPart) contentLines.push(firstContentPart);
                } else if (isContentSection) {
                    contentLines.push(line);
                }
            }
            const contentText = contentLines.join('\n').trim();

            if (maidName && dateString && contentText) {
                const diaryPayload = { maidName, dateString, contentText };
                try {
                    if (DEBUG_MODE) logger.info('日记处理', '调用DailyNoteWrite插件', diaryPayload);
                    // pluginManager.executePlugin is expected to handle JSON stringification if the plugin expects a string
                    // and to parse the JSON response from the plugin.
                    // The third argument to executePlugin in Plugin.js is inputData, which can be a string or object.
                    // For stdio, it's better to stringify here.
                    const pluginResult = await pluginManager.executePlugin("DailyNoteWrite", JSON.stringify(diaryPayload));
                    // pluginResult is the direct parsed JSON object from the DailyNoteWrite plugin's stdout.
                    // Example success: { status: "success", message: "Diary saved to /path/to/your/file.txt" }
                    // Example error:   { status: "error", message: "Error details" }

                    if (pluginResult && pluginResult.status === "success" && pluginResult.message) {
                        const dailyNoteWriteResponse = pluginResult; // Use pluginResult directly

                        if (DEBUG_MODE) logger.info('日记处理', `DailyNoteWrite插件报告成功: ${dailyNoteWriteResponse.message}`);
                        
                        let filePath = '';
                        const successMessage = dailyNoteWriteResponse.message; // e.g., "Diary saved to /path/to/file.txt"
                        const pathMatchMsg = /Diary saved to (.*)/;
                        const matchedPath = successMessage.match(pathMatchMsg);
                        if (matchedPath && matchedPath[1]) {
                            filePath = matchedPath[1];
                        }

                        const notification = {
                            type: 'daily_note_created',
                            data: {
                                maidName: diaryPayload.maidName,
                                dateString: diaryPayload.dateString,
                                filePath: filePath,
                                status: 'success',
                                message: `日记 '${filePath || '未知路径'}' 已为 '${diaryPayload.maidName}' (${diaryPayload.dateString}) 创建成功。`
                            }
                        };
                        webSocketServer.broadcast(notification, 'VCPLog');
                        if (DEBUG_MODE) logger.info('日记处理', '已广播daily_note_created通知', notification);

                    } else if (pluginResult && pluginResult.status === "error") {
                        // Handle errors reported by the plugin's JSON response
                        logger.error('日记处理', `DailyNoteWrite插件报告错误`, pluginResult.message || pluginResult);
                    } else {
                        // Handle cases where pluginResult is null, or status is not "success"/"error", or message is missing on success.
                        logger.error('日记处理', `DailyNoteWrite插件返回了意外的响应结构或失败`, pluginResult);
                    }
                } catch (pluginError) {
                    // This catches errors from pluginManager.executePlugin itself (e.g., process spawn error, timeout)
                    logger.error('日记处理', `执行DailyNoteWrite插件时出错: ${pluginError.message}`, pluginError.stack);
                }
            } else {
                logger.error('日记处理', `无法从日记块中提取女仆名、日期或内容`, { maidName, dateString, contentText: contentText?.substring(0, 50) });
            }
        }
    }
}

// --- Admin API Router (Moved to routes/adminPanelRoutes.js) ---

// Define dailyNoteRootPath here as it's needed by the adminPanelRoutes module
// and was previously defined within the moved block.
const dailyNoteRootPath = path.join(__dirname, 'dailynote');

// Import and use the admin panel routes, passing the getter for currentServerLogPath
const adminPanelRoutes = require('./routes/adminPanelRoutes')(
    DEBUG_MODE,
    dailyNoteRootPath,
    pluginManager,
    () => currentServerLogPath // Getter function for currentServerLogPath
);

app.use('/admin_api', adminPanelRoutes);

// === 系统监控API端点 ===
const systemMonitorRoutes = require('./routes/systemMonitorRoutes');
app.use('/admin_api/monitor', systemMonitorRoutes);

// === 智能记忆系统API端点 ===
const memorySystemRoutes = require('./routes/memorySystemRoutes')(DEBUG_MODE, pluginManager);
app.use('/admin_api/memory', memorySystemRoutes);

// === WeChat适配器API端点（无权限验证） ===
const wechatAdapterModule = require('./routes/wechatAdapterRoutes');
wechatAdapterModule.initializeWebSocket(webSocketServer);
app.use('/admin_api/wechat', wechatAdapterModule.router);

// === MCP插件管理API端点 ===
// 获取所有MCP插件状态
app.get('/admin_api/mcp/plugins', (req, res) => {
    try {
        const mcpPlugins = mcpManager.getAvailableMcpPlugins();
        res.json({
            success: true,
            plugins: mcpPlugins,
            total: mcpPlugins.length,
            enabled: mcpPlugins.filter(p => p.enabled).length
        });
    } catch (error) {
        logger.error('管理面板', `获取MCP插件列表失败: ${error.message}`);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 启用/禁用MCP插件
app.post('/admin_api/mcp/plugins/:pluginName/toggle', (req, res) => {
    try {
        const { pluginName } = req.params;
        const { enabled } = req.body;
        
        const success = mcpManager.setMcpPluginEnabled(pluginName, enabled);
        
        if (success) {
            res.json({
                success: true,
                message: `MCP插件 ${pluginName} 已${enabled ? '启用' : '禁用'}`
            });
        } else {
            res.status(404).json({
                success: false,
                error: `MCP插件 ${pluginName} 不存在`
            });
        }
    } catch (error) {
        logger.error('管理面板', `切换MCP插件状态失败: ${error.message}`);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// 重新加载MCP插件
app.post('/admin_api/mcp/reload', async (req, res) => {
    try {
        await mcpManager.reloadMcpPlugins();
        const plugins = mcpManager.getAvailableMcpPlugins();
        
        res.json({
            success: true,
            message: 'MCP插件重新加载完成',
            plugins: plugins,
            total: plugins.length
        });
    } catch (error) {
        logger.error('管理面板', `重新加载MCP插件失败: ${error.message}`);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// --- End Admin API Router ---

// 新增：异步插件回调路由
const VCP_ASYNC_RESULTS_DIR = path.join(__dirname, 'VCPAsyncResults');

async function ensureAsyncResultsDir() {
    try {
        await fs.mkdir(VCP_ASYNC_RESULTS_DIR, { recursive: true });
    } catch (error) {
        logger.error('服务器设置', `创建 VCPAsyncResults 目录失败: ${VCP_ASYNC_RESULTS_DIR}`, error);
    }
}

app.post('/plugin-callback/:pluginName/:taskId', async (req, res) => {
    const { pluginName, taskId } = req.params;
    const callbackData = req.body; // 这是插件回调时发送的 JSON 数据

    if (DEBUG_MODE) {
            logger.info('服务器回调', `接收到插件 ${pluginName} 的回调，taskId: ${taskId}`);
    logger.info('服务器回调', '回调数据', JSON.stringify(callbackData, null, 2));
    }

    // 1. Save callback data to a file
    await ensureAsyncResultsDir();
    const resultFilePath = path.join(VCP_ASYNC_RESULTS_DIR, `${pluginName}-${taskId}.json`);
    try {
        await fs.writeFile(resultFilePath, JSON.stringify(callbackData, null, 2), 'utf-8');
        if (DEBUG_MODE) logger.info('服务器回调', `已保存 ${pluginName}-${taskId} 的异步结果到 ${resultFilePath}`);
    } catch (fileError) {
        logger.error('服务器回调', `保存 ${pluginName}-${taskId} 异步结果文件时出错`, fileError);
        // Continue with WebSocket push even if file saving fails for now
    }

    const pluginManifest = pluginManager.getPlugin(pluginName);

    if (!pluginManifest) {
        logger.error('服务器回调', `未找到插件 ${pluginName} 的清单文件`);
        // Still attempt to acknowledge the callback if possible, but log error
        return res.status(404).json({ status: "error", message: "Plugin not found, but callback noted." });
    }

    // 2. WebSocket push (existing logic)
    if (pluginManifest.webSocketPush && pluginManifest.webSocketPush.enabled) {
        const targetClientType = pluginManifest.webSocketPush.targetClientType || null;
        const wsMessage = {
            type: pluginManifest.webSocketPush.messageType || 'plugin_callback_notification',
            data: callbackData
        };
        webSocketServer.broadcast(wsMessage, targetClientType);
        if (DEBUG_MODE) {
            logger.info('服务器回调WebSocket', `插件 ${pluginName} (taskId: ${taskId}) 推送已处理`, JSON.stringify(wsMessage, null, 2));
        }
    } else if (DEBUG_MODE) {
        logger.info('服务器回调WebSocket', `插件 ${pluginName} 的WebSocket推送未配置或已禁用`);
    }

    res.status(200).json({ status: "success", message: "Callback received and processed" });
});


async function initialize() {
    logger.system('开始加载插件...');
    await pluginManager.loadPlugins();
    logger.system('插件加载完成');
    pluginManager.setProjectBasePath(__dirname); 
    
    logger.system('开始初始化服务类插件...');
    await pluginManager.initializeServices(app, __dirname); 
    logger.system('服务类插件初始化完成。');

    logger.system('开始初始化静态插件...');
    await pluginManager.initializeStaticPlugins();
    logger.system('静态插件初始化完成。'); // Keep
    
    // 初始化MCP插件系统
    if (VCP_USE_OPENAI_TOOLS) {
        logger.system('开始初始化MCP插件系统...');
        await mcpManager.initialize();
        logger.system('MCP插件系统初始化完成。');
    }

    // 初始化智能情感记忆系统插件
    logger.system('开始初始化智能情感记忆系统插件...');
    try {
        const AdvancedMemorySystemPlugin = require('./Plugin/AdvancedMemorySystem/PluginInterface.js');
        global.advancedMemoryPlugin = new AdvancedMemorySystemPlugin(logger);
        
        // AdvancedMemorySystem插件使用自己的配置文件，不需要主服务器传递配置
        // 只传递基本的功能开关配置，让插件自己加载OpenAI配置
        const pluginConfig = {
            EMOTION_MEMORY_ENABLED: (process.env.EMOTION_MEMORY_ENABLED || 'true').toLowerCase() === 'true',
            AFFINITY_TRACKING_ENABLED: false, // 保持禁用
            CONCEPT_LEARNING_ENABLED: (process.env.CONCEPT_LEARNING_ENABLED || 'true').toLowerCase() === 'true',
            BACKGROUND_THREADS_ENABLED: (process.env.BACKGROUND_THREADS_ENABLED || 'true').toLowerCase() === 'true',
            ENABLE_DETAILED_LOGGING: (process.env.ENABLE_DETAILED_LOGGING || 'true').toLowerCase() === 'true',
            LOG_LEVEL: process.env.LOG_LEVEL || 'info',
            TRANSACTION_TIMEOUT: parseInt(process.env.TRANSACTION_TIMEOUT) || 30,
            ENABLE_ROLLBACK_ON_ERROR: (process.env.ENABLE_ROLLBACK_ON_ERROR || 'true').toLowerCase() === 'true'
        };

        logger.info('情感记忆插件', 'AdvancedMemorySystem将使用自己的config.env配置文件');
        const initResult = await global.advancedMemoryPlugin.initialize(pluginConfig);
        logger.success('情感记忆插件', `智能情感记忆系统插件初始化成功 v${initResult.version}`);
        logger.info('情感记忆插件', `插件功能: ${initResult.capabilities.join(', ')}`);
    } catch (error) {
        logger.error('情感记忆插件', '智能情感记忆系统插件初始化失败:', error.message);
        global.advancedMemoryPlugin = null;
    }
    // EmojiListGenerator (static plugin) is automatically executed as part of the initializeStaticPlugins call above.
    // Its script (`emoji-list-generator.js`) will run and generate/update the .txt files
    // in its `generated_lists` directory. No need to call it separately here.

    if (DEBUG_MODE) logger.system('开始从插件目录加载表情包列表到缓存 (由EmojiListGenerator插件生成)...');
    const emojiListSourceDir = path.join(__dirname, 'Plugin', 'EmojiListGenerator', 'generated_lists');
    cachedEmojiLists.clear();

    try {
        const listFiles = await fs.readdir(emojiListSourceDir);
        const txtFiles = listFiles.filter(file => file.toLowerCase().endsWith('.txt'));

        if (txtFiles.length === 0) {
            if (DEBUG_MODE) logger.warning('初始化', `警告: 在emoji列表源目录中未找到.txt文件: ${emojiListSourceDir}`);
        } else {
            if (DEBUG_MODE) logger.info('初始化', `在 ${emojiListSourceDir} 中找到 ${txtFiles.length} 个emoji列表文件。正在加载...`);
            await Promise.all(txtFiles.map(async (fileName) => {
                const emojiName = fileName.replace(/\.txt$/i, '');
                const filePath = path.join(emojiListSourceDir, fileName);
                try {
                    const listContent = await fs.readFile(filePath, 'utf-8');
                    cachedEmojiLists.set(emojiName, listContent);
                } catch (readError) {
                    logger.error('初始化', `读取emoji列表文件 ${filePath} 时出错: ${readError.message}`);
                    cachedEmojiLists.set(emojiName, `[加载 ${emojiName} 列表失败: ${readError.code}]`);
                }
            }));
            if (DEBUG_MODE) logger.info('初始化', '所有可用的emoji列表已加载到缓存中');
        }
    } catch (error) {
         if (error.code === 'ENOENT') {
             logger.error('初始化', `错误: 未找到emoji列表源目录: ${emojiListSourceDir}。请确保EmojiListGenerator插件成功运行`);
         } else {
            logger.error('初始化', `读取emoji列表源目录 ${emojiListSourceDir} 时出错: ${error.message}`);
         }
    }
    if (DEBUG_MODE) logger.system('表情包列表缓存加载完成。');
}

// Store the server instance globally so it can be accessed by gracefulShutdown
let server;

server = app.listen(port, async () => { // Assign to server variable
    logger.info('调试日志', `中间层服务器正在监听端口 ${port}`);
    logger.info('调试日志', `API 服务器地址: ${apiUrl}`);

    // 初始化配置热更新系统
    global.configHotReload = new ConfigHotReloadManager(logger);
    await global.configHotReload.initialize();

    // 注册全局配置变更回调，特别处理角色特征配置
    global.configHotReload.registerCallback('global', (newConfig, oldConfig) => {
        logger.info('配置热更新', '全局配置已更新，正在同步环境变量...');

        // 更新角色特征相关的环境变量
        const charConfigKeys = [
            'CharPersonalityProfile',
            'CharAppearance',
            'CharHobbies',
            'CharBehaviorPattern',
            'CharSpecialSkills'
        ];

        let charConfigUpdated = false;
        charConfigKeys.forEach(key => {
            if (newConfig[key] !== undefined && newConfig[key] !== process.env[key]) {
                process.env[key] = newConfig[key];
                charConfigUpdated = true;
                logger.success('配置热更新', `角色特征配置已更新: ${key}`);
            }
        });

        if (charConfigUpdated) {
            logger.success('配置热更新', 'MCP模式工具调用将使用新的角色特征配置');
        }

        // 更新其他重要的环境变量
        Object.keys(newConfig).forEach(key => {
            if (process.env[key] !== newConfig[key]) {
                process.env[key] = newConfig[key];
            }
        });

        return { success: true, message: '全局配置已同步到环境变量' };
    });

    // 显示启动信息
    console.log(`
┌─────────────────────────────────────────────────────────────────┐
│                     VCP对话后端服务器                              │
├─────────────────────────────────────────────────────────────────┤
│  端口: ${port}                                                   │
│  状态: 运行中                                                     │
│  模式: ${VCP_USE_OPENAI_TOOLS ? 'MCP模式' : 'VCP模式'}           │
│  时间: ${new Date().toLocaleString()}                            │
├─────────────────────────────────────────────────────────────────┤
│  管理面板: http://localhost:${port}/AdminPanel                   │
│  API文档: 查看 server.js 获取详细信息                             │
└─────────────────────────────────────────────────────────────────┘
    `);
    // ensureDebugLogDir() is effectively handled by initializeServerLogger() synchronously earlier.
    // If ensureDebugLogDirAsync was meant for other purposes, it can be called where needed.
    await initialize(); // This loads plugins and initializes services

    // Initialize the new WebSocketServer
            if (DEBUG_MODE) logger.info('服务器', '正在初始化WebSocket服务器...');
    const vcpKeyValue = pluginManager.getResolvedPluginConfigValue('VCPLog', 'VCP_Key') || process.env.VCP_Key;
    webSocketServer.initialize(server, { debugMode: DEBUG_MODE, vcpKey: vcpKeyValue });
            if (DEBUG_MODE) logger.info('服务器', 'WebSocket服务器已初始化');

    // The VCPLog plugin's attachWebSocketServer is no longer needed here as WebSocketServer handles it.
    // const vcpLogPluginModule = pluginManager.serviceModules.get("VCPLog")?.module;
    // if (vcpLogPluginModule && typeof vcpLogPluginModule.attachWebSocketServer === 'function') {
    //     if (DEBUG_MODE) logger.system('[Server] Attaching WebSocket server for VCPLog plugin...');
    //     vcpLogPluginModule.attachWebSocketServer(server); // Pass the http.Server instance
    // } else {
    //     if (DEBUG_MODE) console.warn('[Server] VCPLog plugin module or attachWebSocketServer function not found.');
    // }

    // 自动打开管理面板
    if (ADMIN_USERNAME && ADMIN_PASSWORD) {
        setTimeout(async () => {
            try {
                const { default: open } = await import('open');
                const adminUrl = `http://localhost:${port}/AdminPanel`;

                await open(adminUrl);
                logger.system('已自动打开管理面板');
            } catch (error) {
                logger.warning('自动打开管理面板失败', error.message);
                logger.info('手动访问', `请手动打开: http://localhost:${port}/AdminPanel`);
            }
        }, 2000); // 延迟2秒确保服务器完全启动
    } else {
        logger.warning('管理面板', '未配置管理员账号，请在config.env中设置AdminUsername和AdminPassword');
    }
});

async function gracefulShutdown() {
    logger.system('Initiating graceful shutdown...'); // This will be logged
    if (webSocketServer) { // Shutdown WebSocketServer
        logger.info('服务器', '正在关闭WebSocket服务器...');
        webSocketServer.shutdown();
    }
    if (pluginManager) {
        await pluginManager.shutdownAllPlugins();
    }

    if (serverLogWriteStream) {
        logger.info('服务器', '正在关闭服务器日志文件流...');
        const logClosePromise = new Promise((resolve) => {
            serverLogWriteStream.end(`[${new Date().toLocaleString()}] Server gracefully shut down.\n`, () => {
                originalConsoleLog('[Server] Server log stream closed.'); // Use original console here as overridden one might rely on the stream
                resolve();
            });
        });
        await logClosePromise; // Wait for log stream to close
    }

    logger.system('Graceful shutdown complete. Exiting.');
    process.exit(0);
}

process.on('SIGINT', gracefulShutdown);
process.on('SIGTERM', gracefulShutdown);

// Ensure log stream is flushed on uncaught exceptions or synchronous exit, though less reliable
process.on('exit', (code) => {
    originalConsoleLog(`[Server] Exiting with code ${code}.`);
    if (serverLogWriteStream && !serverLogWriteStream.destroyed) {
        try {
            // Attempt a final synchronous write if possible, though not guaranteed
            fsSync.appendFileSync(currentServerLogPath, `[${new Date().toLocaleString()}] Server exited with code ${code}.\n`);
            serverLogWriteStream.end(); // Attempt to close if not already
        } catch (e) {
            originalConsoleError('[Server] Error during final log write on exit:', e.message);
        }
    }
});

// === MCP/OpenAI Tools 支持函数 ===
// 获取可用的OpenAI Tools列表 (现在通过MCP管理器提供)
function getAvailableOpenAITools() {
    return mcpManager.getOpenAITools();
}

// 将OpenAI Function调用转换为MCP插件调用
function convertOpenAIFunctionToMCP(functionCall) {
    const mcpPluginName = functionCall.name;
    const args = JSON.parse(functionCall.arguments);
    
    return {
        pluginName: mcpPluginName,
        args: args
    };
}

// MCP模式工具调用已集成到handleMCPRequest函数中

// 处理MCP模式请求
async function handleMCPRequest(req, res) {
    const { default: fetch } = await import('node-fetch');
    
    try {
        const originalBody = req.body;
        
        // === 新增：在最开始就保存完全原始的用户消息，用于图片渲染 ===
        let originalUserMessageForRender = '';
        if (originalBody.messages && Array.isArray(originalBody.messages)) {
            // 找到最后一个用户消息
            for (let i = originalBody.messages.length - 1; i >= 0; i--) {
                if (originalBody.messages[i].role === 'user') {
                    // 处理字符串类型的content
                    if (typeof originalBody.messages[i].content === 'string') {
                        originalUserMessageForRender = originalBody.messages[i].content;
                    }
                    // 处理数组类型的content（多模态消息）
                    else if (Array.isArray(originalBody.messages[i].content)) {
                        const textParts = originalBody.messages[i].content
                            .filter(part => part.type === 'text')
                            .map(part => part.text || '')
                            .join(' ');
                        originalUserMessageForRender = textParts;
                    }
                    break;
                }
            }
        }
        
        // === 新增：提取对话记录相关参数 ===
        const memoryTracking = originalBody.memory_tracking === true;
        const enableContext = originalBody.enable_context === true;
        const userId = originalBody.userId || originalBody.user_id || 'anonymous';
        const userMessage = originalBody.messages ? 
            originalBody.messages.filter(m => m.role === 'user').pop()?.content || '' : '';
        
        // === 新增：提取VCP过滤参数（MCP模式） ===
        const censorVcpOutput = originalBody.censor_vcp_output === true;
        const vcpContentMaxLength = originalBody.vcp_content_max_length;

        // 移除这行：let originalUserMessageForRender = userMessage; // 保存原始用户消息，用于图片渲染
        
        // === 新增：动态上下文注入（MCP模式）===
        let cleanedBody = { ...originalBody };
        if (memoryTracking && enableContext && userId !== 'anonymous' && userMessage) {
            try {
                // 获取assistantName用于上下文生成
                const assistantName = originalBody.assistantName || originalBody.assistant_name || 'Assistant';

                // 使用智能情感记忆系统插件获取智能上下文
                if (global.advancedMemoryPlugin) {
                    logger.info('上下文注入', `MCP模式：开始生成智能上下文 [${userId}]`);
                    const contextSummary = await generateIntelligentContext(userId, userMessage, originalBody.maxContextSize || 15, assistantName);
                    
                    logger.debug('上下文注入', `MCP模式：智能上下文生成结果 [${userId}]`, {
                        hasContext: !!contextSummary,
                        contextType: typeof contextSummary,
                        contextLength: contextSummary ? contextSummary.length : 0,
                        contextPreview: contextSummary ? contextSummary.substring(0, 100) + '...' : 'null'
                    });

                    // 获取System级心理状态强制指令
                    logger.info('心理状态', `MCP模式：开始生成心理状态指令 [${userId}]`);
                    const psychologyInstructions = await global.advancedMemoryPlugin.generateSystemPsychologyInstructions(userId, assistantName);
                    
                    logger.debug('心理状态', `MCP模式：心理状态指令生成结果 [${userId}]`, {
                        hasPsychology: !!psychologyInstructions,
                        psychologyType: typeof psychologyInstructions,
                        psychologyLength: psychologyInstructions ? psychologyInstructions.length : 0,
                        psychologyPreview: psychologyInstructions ? psychologyInstructions.substring(0, 100) + '...' : 'null'
                    });

                    // 合并上下文和心理状态指令
                    let fullSystemContent = '';
                    if (contextSummary && contextSummary.trim()) {
                        fullSystemContent += contextSummary;
                        logger.debug('上下文注入', `MCP模式：添加智能上下文到fullSystemContent [${userId}]`);
                    } else {
                        logger.warning('上下文注入', `MCP模式：智能上下文为空，跳过添加 [${userId}]`);
                    }
                    if (psychologyInstructions && psychologyInstructions.trim()) {
                        fullSystemContent += psychologyInstructions;
                        logger.debug('心理状态', `MCP模式：添加心理状态指令到fullSystemContent [${userId}]`);
                        logger.info('心理状态', `MCP模式为用户 ${userId} (${assistantName}) 注入了心理状态指令，长度: ${psychologyInstructions.length}`);
                    } else {
                        logger.warning('心理状态', `MCP模式：心理状态指令为空，跳过添加 [${userId}]`);
                    }

                    // 调试：打印心理状态指令内容
                    //console.log('\n' + '='.repeat(60));
                    //console.log('🧠 心理状态强制指令内容:');
                    //console.log('='.repeat(60));
                    //console.log(psychologyInstructions);
                    //console.log('='.repeat(60) + '\n');

                    if (fullSystemContent.trim()) {
                        // 查找并更新system消息
                        let hasSystemMessage = false;
                        cleanedBody.messages = cleanedBody.messages.map(msg => {
                            if (msg.role === 'system') {
                                hasSystemMessage = true;
                                return {
                                    ...msg,
                                    content: msg.content + fullSystemContent
                                };
                            }
                            return msg;
                        });

                        // 如果没有system消息，添加一个
                        if (!hasSystemMessage) {
                            cleanedBody.messages.unshift({
                                role: 'system',
                                content: `你是一个智能助手。${fullSystemContent}`
                            });
                        }

                        logger.info('智能上下文', `MCP模式为用户 ${userId} (${assistantName}) 注入了完整system内容，长度: ${fullSystemContent.length}`);

                        // 调试：完整打印system消息内容
                        if (DEBUG_MODE) {
                            //console.log('\n' + '='.repeat(80));
                            //console.log('🔍 MCP模式完整的System消息内容:');
                            //console.log('='.repeat(80));
                            //console.log(fullSystemContent);
                            //console.log('='.repeat(80));
                            //console.log(`📏 System消息长度: ${fullSystemContent.length} 字符\n`);
                        }
                        
                        // 调试：打印注入后的system消息
                        const systemMsg = cleanedBody.messages.find(msg => msg.role === 'system');
                        if (systemMsg) {
                            logger.debug('上下文调试', `System消息内容 (前200字符): ${systemMsg.content.substring(0, 200)}...`);
                        }
                    } else {
                        logger.debug('智能上下文', `MCP模式为用户 ${userId} 生成的上下文为空`);
                    }
                } else {
                    // 回退到原有的ConversationLogger
                    const ConversationLogger = require('./utils/conversationLogger');
                    const contextLogger = new ConversationLogger();
                    
                    const relevantContext = await contextLogger.getRelevantContext(userId, userMessage, {
                        maxContextSize: originalBody.maxContextSize || 15
                    });
                    
                    if (relevantContext && relevantContext.length > 0) {
                        const contextMessage = contextLogger.formatContextForSystem(relevantContext, userMessage, {
                            maxContextSize: originalBody.maxContextSize || 15
                        });
                        
                        let hasSystemMessage = false;
                        cleanedBody.messages = cleanedBody.messages.map(msg => {
                            if (msg.role === 'system') {
                                hasSystemMessage = true;
                                return {
                                    ...msg,
                                    content: msg.content + contextMessage
                                };
                            }
                            return msg;
                        });
                        
                        if (!hasSystemMessage) {
                            cleanedBody.messages.unshift({
                                role: 'system',
                                content: `你是一个智能助手。${contextMessage}`
                            });
                        }
                        
                        logger.info('mcp模式', `MCP模式为用户 ${userId} 注入了 ${relevantContext.length} 条相关上下文（回退模式）`);
                    }
                }
            } catch (contextError) {
                logger.warning('MCP模式', 'MCP模式动态上下文注入失败:', contextError.message);
            }
        }
        
        // === 新增：Agent参数处理（MCP模式） ===
        await processMcpAgentParameter(originalBody, cleanedBody, AGENT_DIR);

        // === 新增：处理userId前缀和本地图片路径解析（MCP模式） ===
        // 获取真正的原始用户消息（不包含任何处理）
        const lastUserMessage = cleanedBody.messages.find(msg => msg.role === 'user');
        let originalUserContent = lastUserMessage ? lastUserMessage.content : '';

        // 如果content是数组格式，提取文本内容
        if (Array.isArray(originalUserContent)) {
            const textContent = originalUserContent.find(item => item.type === 'text');
            originalUserContent = textContent ? textContent.text : '';
        }

        if (DEBUG_MODE) {
            logger.info('消息处理(MCP)', `原始用户消息: ${originalUserContent.substring(0, 100)}...`);
        }

        // 使用组件处理用户消息
        let processedUserMessage = processUserMessage(originalUserContent, cleanedBody, DEBUG_MODE);

        // 应用到最后一个用户消息
        if (cleanedBody.messages && Array.isArray(cleanedBody.messages)) {
            const lastUserMessageIndex = cleanedBody.messages.map(msg => msg.role).lastIndexOf('user');
            if (lastUserMessageIndex !== -1) {
                cleanedBody.messages[lastUserMessageIndex].content = processedUserMessage;
            }
        }

        // === 新增：useragent参数处理（MCP模式） ===
        await processMcpUserAgentParameter(originalBody, cleanedBody, AGENT_DIR);

        // === 新增：聊天上下文参数处理（MCP模式） ===
        await processMcpChatContextParameter(originalBody, cleanedBody);

        // === 打印详细的messages数组内容（MCP模式） ===
        //console.log('\n=== 🔧 MCP模式Messages数组 ===');
        //console.log(`时间: ${new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}`);
        //console.log(`用户: ${originalBody.userId || 'N/A'}, 助手: ${originalBody.assistantName || 'N/A'}, 长度: ${cleanedBody.messages.length}`);
        //console.log('Messages JSON:');
        //console.log(JSON.stringify(cleanedBody.messages, null, 2));
        //console.log('=== 🔧 MCP模式Messages数组内容结束 ===\n');

        // 清理请求体，移除自定义参数避免传递给AI API
        cleanRequestBody(cleanedBody);
        
        logger.info('MCP工具', '开始MCP模式处理');
        
        // 预处理消息（图片处理等）
        let processedBody = JSON.parse(JSON.stringify(cleanedBody));
        
        // 调试：检查processedBody是否包含上下文
        const systemMsgInProcessed = processedBody.messages.find(msg => msg.role === 'system');
        if (systemMsgInProcessed) {
            logger.debug('上下文调试', `processedBody System消息内容 (前200字符): ${systemMsgInProcessed.content.substring(0, 200)}...`);
        } else {
            logger.debug('上下文调试', 'processedBody中没有system消息');
        }
        

        
        // 变量替换
        if (processedBody.messages && Array.isArray(processedBody.messages)) {
            processedBody.messages = await processMessagesVariableReplacement(processedBody.messages, processedBody.model, AGENT_DIR);
        }


        
        // 构建可用的工具列表（如果没有提供tools，则使用所有可用的MCP工具）
        if (!processedBody.tools) {
            processedBody.tools = getAvailableOpenAITools();
            logger.info('MCP工具', `自动添加所有可用工具，共${processedBody.tools.length}个`);
        }

        // 高级语义理解工具判断系统
        const assistantName = originalBody.assistantName || originalBody.assistant_name || 'Assistant';
        const userRequest = processedBody.messages[processedBody.messages.length - 1]?.content || '';

        // 构建工具信息数组
        const availableToolsInfo = processedBody.tools.map(tool => {
            const func = tool.function;
            return {
                name: func.name,
                description: func.description
            };
        });

        // 获取用户参数
        const actualUserId = originalBody.userId || originalBody.user_id || 'anonymous';
        const actualUserAgent = originalBody.useragent || originalBody.userAgent || actualUserId;
        const actualAgent = originalBody.agent || 'Assistant';
        const actualAssistantName = assistantName;

        // 保持完整的用户信息
        const userName = actualUserId;
        const agentName = actualAgent;
        const assistantDisplayName = actualAssistantName;

        // 构建上下文信息
        const contextInfo = {
            assistantName: assistantName,
            userId: actualUserId,
            userAgent: actualUserAgent,
            agent: actualAgent,
            userName: userName,
            agentName: agentName,
            assistantDisplayName: assistantDisplayName
        };

        // 第一步：添加工具需求分析工具，强制调用来分析需要哪些工具
        const analysisTools = [
            {
                type: 'function',
                function: {
                    name: 'analyze_tool_requirements',
                    description: '深度分析用户请求，智能判断需要调用哪些工具以及最优的调用顺序',
                    parameters: {
                        type: 'object',
                        properties: {
                            needs_tools: {
                                type: 'boolean',
                                description: '是否需要调用工具来完成用户请求。普通聊天为false，功能需求为true'
                            },
                            required_tools: {
                                type: 'array',
                                items: {
                                    type: 'string',
                                    enum: availableToolsInfo.map(tool => tool.name)
                                },
                                description: `需要调用的工具名称列表，严格按照逻辑执行顺序排列。【重要】只能使用以下可用工具：${availableToolsInfo.map(tool => tool.name).join(', ')}。如果${contextInfo.userName}需要多次执行同一工具（如画3张图），在数组中重复列出工具名称，例如：["NovelAIGen","NovelAIGen","NovelAIGen"]`
                            },
                            reasoning: {
                                type: 'string',
                                description: `详细的分析推理过程，需要包含：1)${contextInfo.userName}的具体需求分析 2)选择这些工具的原因 3)执行顺序的逻辑 4)预期达成的效果。要用具体的人名（${contextInfo.userName}、${contextInfo.assistantDisplayName}）、地名、事件来描述，避免使用"用户"、"AI"等抽象词汇`
                            }
                        },
                        required: ['needs_tools', 'required_tools', 'reasoning']
                    }
                }
            }
        ];

        // 获取AI最近行为和对话历史 - 使用数据库方法
        let aiLastMessage = null;
        let conversationContext = [];

        try {
            // 使用智能情感记忆系统插件获取最近记忆
            if (global.advancedMemoryPlugin && global.advancedMemoryPlugin.embeddingService) {
                const recentMemories = await global.advancedMemoryPlugin.embeddingService.getRecentMemories(userId, assistantName, 10);

                if (recentMemories && recentMemories.length > 0) {
                    // 获取最近的AI回复
                    for (let i = 0; i < recentMemories.length; i++) {
                        const memory = recentMemories[i];
                        if (memory.persona_name === assistantName && memory.original_content && memory.original_content.ai_response) {
                            aiLastMessage = memory.original_content.ai_response;
                            break;
                        }
                    }

                    // 获取最近3轮对话作为上下文
                    conversationContext = recentMemories.slice(0, 3).map(memory => ({
                        user: memory.original_content.user_message || '',
                        ai: memory.original_content.ai_response || '',
                        timestamp: memory.creation_time
                    }));
                }
            } else {
                logger.warning('MCP工具', '智能情感记忆系统插件未初始化，无法获取对话历史');
            }
        } catch (error) {
            logger.warning('MCP工具', `获取对话上下文失败: ${error.message}`);
        }

        // 使用AI直接判断工具调用需求
        logger.info('MCP工具', `开始AI工具调用判断`);

        // 更新上下文信息，添加对话历史
        contextInfo.aiLastMessage = aiLastMessage || '无最近消息';
        contextInfo.conversationHistory = conversationContext.length > 0 ?
            conversationContext.map(ctx => `${userName}: ${ctx.user} | ${assistantDisplayName}: ${ctx.ai}`).join('\n') :
            '无对话历史';

        // 获取角色特征配置 - 使用配置热更新系统
        let charPersonalityProfile, charAppearance, charHobbies, charBehaviorPattern, charSpecialSkills;

        // 优先从配置热更新系统获取最新配置
        if (global.configHotReload) {
            const globalConfig = global.configHotReload.getConfig('global');
            charPersonalityProfile = globalConfig.CharPersonalityProfile || process.env.CharPersonalityProfile || '';
            charAppearance = globalConfig.CharAppearance || process.env.CharAppearance || '';
            charHobbies = globalConfig.CharHobbies || process.env.CharHobbies || '';
            charBehaviorPattern = globalConfig.CharBehaviorPattern || process.env.CharBehaviorPattern || '';
            charSpecialSkills = globalConfig.CharSpecialSkills || process.env.CharSpecialSkills || '';

            logger.debug('配置热更新', `使用热更新配置获取角色特征: 性格=${!!charPersonalityProfile}, 外观=${!!charAppearance}, 爱好=${!!charHobbies}, 行为=${!!charBehaviorPattern}, 技能=${!!charSpecialSkills}`);
        } else {
            // 回退到环境变量
            charPersonalityProfile = process.env.CharPersonalityProfile || '';
            charAppearance = process.env.CharAppearance || '';
            charHobbies = process.env.CharHobbies || '';
            charBehaviorPattern = process.env.CharBehaviorPattern || '';
            charSpecialSkills = process.env.CharSpecialSkills || '';

            logger.debug('配置热更新', '配置热更新系统未初始化，使用环境变量获取角色特征');
        }

        // 构建接收者角色特征描述
        let receiverCharacteristics = '';
        if (charPersonalityProfile || charAppearance || charHobbies || charBehaviorPattern || charSpecialSkills) {
            receiverCharacteristics = '\n\n# 接收者特征\n';
            if (charPersonalityProfile) receiverCharacteristics += `性格特征：${charPersonalityProfile}\n`;
            if (charAppearance) receiverCharacteristics += `外观描述：${charAppearance}\n`;
            if (charHobbies) receiverCharacteristics += `兴趣爱好：${charHobbies}\n`;
            if (charBehaviorPattern) receiverCharacteristics += `行为模式：${charBehaviorPattern}\n`;
            if (charSpecialSkills) receiverCharacteristics += `特殊技能：${charSpecialSkills}\n`;
        }

        const analysisRequest = {
            model: OPENAI_TOOLS_MODEL,
            messages: [
                {
                    role: "system",
                    content: `你是智能工具分析助手，负责根据用户的输入内容和对话上下文，判断接收者是否需要使用工具以及需要使用哪些工具来完成任务

当前对话情况：
- 接收者：${contextInfo.assistantName}（${contextInfo.assistantDisplayName}）
- 接收者的特征: ${receiverCharacteristics}
- 用户：${contextInfo.userId}（${contextInfo.userName}）
- Agent设定：${contextInfo.agent}（${contextInfo.agentName}）
- UserAgent：${contextInfo.userAgent}
- 接收者最近回复：${contextInfo.aiLastMessage}
- 对话历史：
${contextInfo.conversationHistory}

分析规则：
1. 仔细分析用户${contextInfo.userName}的真实意图和具体需求
2. 如果用户只是想聊天、问候、询问日常话题，设置needs_tools为false
3. 如果用户需要执行特定功能（绘图、搜索、计算等），设置needs_tools为true并为接收者选择相应工具
4. 根据对话上下文和用户的习惯为接收者选择合适的工具
5. 如果接收者需要多个工具，按逻辑顺序排序（例如：先搜索信息，再根据搜索结果绘图）
6. 工具之间可能存在依赖关系，需要考虑执行顺序
7. 考虑接收者的特征和能力，选择最适合其角色的工具组合

【重要】工具限制：
- 只能使用下列可用工具，不得调用不存在的工具
- 如果用户要求的功能没有对应工具，设置needs_tools为false

【重要】多工具执行机制：
- 系统会逐个强制执行工具，而不是同时执行
- 执行流程：执行工具1 → 接收者生成回复1 → 写入数据 → 强制执行工具2 → 接收者生成回复2 → 写入数据 → 依此类推
- 每个工具的执行结果都会被接收者处理并生成回复，然后基于这个回复执行下一个工具
- 这确保了工具间的深度依赖和连续性，符合接收者的角色特征

【重要】多次执行同一工具：
- 如果用户要求多次执行同一工具（如"画3张图片"），在required_tools数组中重复列出工具名称
- 例如：画3张图片 → ["NovelAIGen","NovelAIGen","NovelAIGen"]
- 例如：搜索2个不同主题 → ["TavilySearch","TavilySearch"]
- 例如：生成5首歌曲 → ["SunoGen","SunoGen","SunoGen","SunoGen","SunoGen"]
- 例如：计算多个数学问题 → ["SciCalculator","SciCalculator","SciCalculator"]

工具分类：
${availableToolsInfo.map((tool, index) =>
    `${index + 1}. ${tool.name} - ${tool.description}`
).join('\n\n')}

判断标准：
- ${contextInfo.userName}只是想聊天、问候、询问日常话题 → needs_tools: false
- ${contextInfo.userName}明确要求功能性操作（画图、搜索、计算等） → needs_tools: true
- ${contextInfo.userName}发送图片且需要处理/分析 → needs_tools: true
- ${contextInfo.userName}@${contextInfo.assistantDisplayName}且有特定互动意图 → needs_tools: true

多工具场景示例（逐个强制执行）：
- "搜索一下北京的天气然后画一张图" → 1.TavilySearch执行完成生成回复 → 2.基于回复强制执行NovelAIGen
- "查找这首歌的信息并制作思维导图" → 1.TavilySearch执行完成生成回复 → 2.基于回复强制执行相关工具
- "分析这张图片然后根据内容绘制新图" → 1.图片分析工具执行完成生成回复 → 2.基于回复强制执行NovelAIGen
- "画3张不同风格的猫咪图片" → ["NovelAIGen","NovelAIGen","NovelAIGen"]
- "搜索人工智能和量子计算的信息" → ["TavilySearch","TavilySearch"]`
                },
                {
                    role: "user",
                    content: "你好"
                },
                {
                    role: "assistant",
                    content: `{"needs_tools":false,"required_tools":[],"reasoning":"${contextInfo.userName}只是在向${contextInfo.assistantDisplayName}打招呼，这是日常的问候交流，${contextInfo.assistantDisplayName}可以直接回应，不需要调用任何特殊工具来完成这个简单的社交互动"}`
                },
                {
                    role: "user",
                    content: "画一张猫咪的图片"
                },
                {
                    role: "assistant",
                    content: `{"needs_tools":true,"required_tools":["NovelAIGen"],"reasoning":"${contextInfo.userName}明确要求${contextInfo.assistantDisplayName}为她画一张猫咪的图片，这是典型的图像生成需求。${contextInfo.assistantDisplayName}需要使用NovelAIGen工具来创作一张可爱的猫咪图片，满足${contextInfo.userName}的绘画请求"}`
                },
                {
                    role: "user",
                    content: "画3张不同风格的猫咪图片"
                },
                {
                    role: "assistant",
                    content: `{"needs_tools":true,"required_tools":["NovelAIGen","NovelAIGen","NovelAIGen"],"reasoning":"${contextInfo.userName}希望${contextInfo.assistantDisplayName}为她创作3张不同风格的猫咪图片，这需要${contextInfo.assistantDisplayName}连续使用3次NovelAIGen工具。第一次画写实风格的猫咪，第二次画卡通风格的猫咪，第三次画水彩风格的猫咪，为${contextInfo.userName}提供丰富多样的猫咪图片选择"}`
                },
                {
                    role: "user",
                    content: "搜索一下最新的AI技术然后根据内容画个思维导图"
                },
                {
                    role: "assistant",
                    content: `{"needs_tools":true,"required_tools":["TavilySearch"],"reasoning":"${contextInfo.userName}想了解最新的AI技术发展情况，并希望${contextInfo.assistantDisplayName}帮她制作一个思维导图来整理信息。${contextInfo.assistantDisplayName}需要先使用TavilySearch搜索当前AI技术的最新进展，获取包括机器学习、深度学习、自然语言处理等领域的前沿信息，然后基于搜索结果为${contextInfo.userName}整理出清晰的知识结构"}`
                },
                {
                    role: "user",
                    content: "帮我搜索3个不同的主题：人工智能、量子计算、生物技术"
                },
                {
                    role: "assistant",
                    content: `{"needs_tools":true,"required_tools":["TavilySearch","TavilySearch","TavilySearch"],"reasoning":"${contextInfo.userName}希望${contextInfo.assistantDisplayName}帮她深入了解三个前沿科技领域的发展现状。${contextInfo.assistantDisplayName}需要分别搜索：1)人工智能领域的最新突破和应用案例 2)量子计算技术的研究进展和实际应用 3)生物技术在医疗健康方面的创新成果。通过三次独立的搜索，为${contextInfo.userName}提供这些领域的全面信息"}`
                },
                {
                    role: 'user',
                    content: `${userRequest}`
                }
            ],
            tools: analysisTools,
            tool_choice: {
                type: 'function',
                function: { name: 'analyze_tool_requirements' }
            },
            temperature: 0.1
        };
        
        logger.info('MCP工具', `步骤1：分析工具需求`);
        console.log(JSON.stringify(analysisRequest, null, 2))
        const analysisResponse = await fetch(`${OPENAI_TOOLS_URL}/v1/chat/completions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${OPENAI_TOOLS_KEY}`
            },
            body: JSON.stringify(analysisRequest)
        });
        
        if (!analysisResponse.ok) {
            const errorText = await analysisResponse.text();
            throw new Error(`OpenAI工具分析失败: ${analysisResponse.status} ${analysisResponse.statusText} - ${errorText}`);
        }
        
        const analysisResult = await analysisResponse.json();
        const analysisMessage = analysisResult.choices[0].message;
        
        // 解析分析结果
        let analysisData = null;
        if (analysisMessage.tool_calls && analysisMessage.tool_calls.length > 0) {
            try {
                analysisData = JSON.parse(analysisMessage.tool_calls[0].function.arguments);
                logger.info('MCP工具', `解析到的分析数据: ${JSON.stringify(analysisData, null, 2)}`);
            } catch (error) {
                logger.error('MCP工具', `解析分析结果失败: ${error.message}`);
                logger.error('MCP工具', `原始参数: ${analysisMessage.tool_calls[0].function.arguments}`);
                throw new Error('工具需求分析失败');
            }
        } else {
            logger.error('MCP工具', `AI没有调用analyze_tool_requirements工具`);
            logger.error('MCP工具', `AI回复: ${JSON.stringify(analysisMessage, null, 2)}`);
        }

        if (!analysisData || !analysisData.needs_tools) {
            logger.info('MCP工具', `分析结果：${analysisData?.reasoning || '无需调用工具'}`);
            logger.info('MCP工具', `决定：不调用工具，直接对话回复`);

            // === 新增：为直接对话也添加工具分析结果到system消息中 ===
            const toolAnalysisInfo = formatToolAnalysisForSystem(analysisData || { needs_tools: false, required_tools: [], reasoning: '普通对话，无需工具支持' }, '无', 0, contextInfo);
            let directMessages = [...processedBody.messages];

            // 查找并更新system消息，添加工具分析信息
            let hasSystemMessage = false;
            directMessages = directMessages.map(msg => {
                if (msg.role === 'system') {
                    hasSystemMessage = true;
                    return {
                        ...msg,
                        content: msg.content + toolAnalysisInfo
                    };
                }
                return msg;
            });

            // 如果没有system消息，创建一个新的
            if (!hasSystemMessage) {
                directMessages.unshift({
                    role: 'system',
                    content: `你是一个智能助手。${toolAnalysisInfo}`
                });
            }

            logger.info('MCP工具', `已将工具分析结果添加到直接对话的system消息中`);

            // 直接使用用户的原始请求进行对话，不调用任何工具
            const directRequest = {
                model: processedBody.model,
                messages: directMessages,
                temperature: processedBody.temperature || 0.7,
                // 保留用户的其他参数，但排除tools
                ...(processedBody.top_p && { top_p: processedBody.top_p }),
                ...(processedBody.frequency_penalty && { frequency_penalty: processedBody.frequency_penalty }),
                ...(processedBody.presence_penalty && { presence_penalty: processedBody.presence_penalty }),
                ...(processedBody.stream && { stream: processedBody.stream })
            };

            // 调试：确认directRequest不包含tools参数
            if (directRequest.tools) {
                logger.warning('MCP工具', '⚠️ directRequest意外包含tools参数，这可能导致问题');
            } else {
                logger.success('MCP工具', '✅ directRequest正确排除了tools参数');
            }

           //cone.log(JSON.stringify(directMessages, null, 2))
            const directResponse = await fetch(`${apiUrl}/v1/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${apiKey}`,
                    ...(req.headers['user-agent'] && { 'User-Agent': req.headers['user-agent'] }),
                    'Accept': directRequest.stream ? 'text/event-stream' : (req.headers['accept'] || 'application/json'),
                },
                body: JSON.stringify(directRequest),
            });
            
            // 处理直接对话响应
            if (directRequest.stream) {
                // 流式响应处理 - 需要应用过滤
            if (!res.headersSent) {
                    res.setHeader('Content-Type', 'text/event-stream');
                    res.setHeader('Cache-Control', 'no-cache');
                    res.setHeader('Connection', 'keep-alive');
                }
                
                // 对于流式响应，我们需要收集所有内容后统一过滤
                if (censorVcpOutput || (typeof vcpContentMaxLength === 'number' && vcpContentMaxLength > 0)) {
                    let collectedContent = '';
                    let allChunks = [];
                    
                    directResponse.body.on('data', (chunk) => {
                        allChunks.push(chunk);
                        const chunkString = chunk.toString('utf-8');
                        
                        // 收集内容用于过滤
                        const lines = chunkString.split('\n');
                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                const jsonData = line.substring(5).trim();
                                if (jsonData !== '[DONE]' && jsonData) {
                                    try {
                                        const parsedData = JSON.parse(jsonData);
                                        collectedContent += parsedData.choices?.[0]?.delta?.content || '';
                                    } catch (e) { /* ignore */ }
                                }
                            }
                        }
                    });
                    
                    directResponse.body.on('end', () => {
                        // 过滤收集到的内容
                        const filteredContent = applyVcpFiltering(collectedContent, {
                            censor_vcp_output: censorVcpOutput,
                            vcp_content_max_length: vcpContentMaxLength
                        });
                        
                        if (DEBUG_MODE) {
                            logger.info('MCP过滤', `直接对话流式内容已过滤，原长度: ${collectedContent.length}, 过滤后长度: ${filteredContent.length}`);
                        }
                        
                        // 重新构建过滤后的流式响应
                        if (!res.writableEnded) {
                            // 创建新的响应数据包含过滤后的内容
                            const responseData = {
                                id: `chatcmpl-MCP-filtered-${Date.now()}`,
                                object: "chat.completion",
                                created: Math.floor(Date.now() / 1000),
                                model: cleanedBody.model || "unknown",
                                choices: [{
                                    index: 0,
                                    message: {
                                        role: "assistant",
                                        content: filteredContent
                                    },
                                    finish_reason: "stop"
                                }]
                            };
                            
                            res.json(responseData);
                        }
                    });
                } else {
                    // 无需过滤，直接转发
                directResponse.body.pipe(res);
                }
            } else {
                // 非流式响应处理
                const responseBuffer = await directResponse.arrayBuffer();
                const responseText = Buffer.from(responseBuffer).toString('utf-8');
                let responseJson = JSON.parse(responseText);

                // 应用VCP过滤（MCP直接对话）
                let filteredContent = responseJson.choices[0].message.content;
                if (censorVcpOutput || (typeof vcpContentMaxLength === 'number' && vcpContentMaxLength > 0)) {
                    filteredContent = applyVcpFiltering(filteredContent, {
                        censor_vcp_output: censorVcpOutput,
                        vcp_content_max_length: vcpContentMaxLength
                    });
                    responseJson.choices[0].message.content = filteredContent;
                }

                // 处理图片渲染
                if (originalBody.render_as_image === true) {
                    try {
                        const HtmlToImageRenderer = require('./Puppeteer/HtmlToImageRenderer');
                        const renderer = new HtmlToImageRenderer();
                        
                        // 准备渲染数据
                        const renderData = {
                            model: cleanedBody.model || 'AI助手',
                            id1: originalBody.user_id || originalBody.userId || '10000',
                            name: originalBody.user_name || originalBody.userId || '用户',
                            MSG: originalUserMessageForRender || '', // 使用保存的原始消息
                            id2: originalBody.assistant_id || '10001',
                            name1: originalBody.assistant_name || originalBody.assistantName || 'AI助手',
                            CONTENT: filteredContent, // 使用过滤后的内容
                            theme: originalBody.theme || 'dark'
                        };

                        // 渲染图片
                        const imageUrl = await renderer.renderToImage(renderData);
                        
                        // 添加图片URL到响应
                        responseJson.image_url = `http://${req.headers.host}${imageUrl}`;
                        
                        // 关闭浏览器实例
                        await renderer.close();
                        
                        logger.info('图片渲染', `成功渲染对话图片: ${imageUrl}`);
                    } catch (renderError) {
                        logger.error('图片渲染', `渲染对话图片失败: ${renderError.message}`);
                        responseJson.image_error = renderError.message;
                    }
                }

                // === 新增：MCP日记处理功能（直接对话） ===
                await handleDiaryFromAIResponse(responseText);

                // === 新增：MCP直接对话记录功能 ===
                if (memoryTracking) {
                    try {
                        // === 调用智能情感记忆系统记录 ===
                        await recordEmotionMemory(
                            userId,
                            originalUserContent,
                            filteredContent,
                            originalBody.assistantName || originalBody.assistant_name || 'Assistant',
                            {
                                type: 'mcp_direct',
                                model: cleanedBody.model || 'unknown',
                                ip: req.ip || req.connection.remoteAddress || 'unknown',
                                stream: false,
                                toolCalls: false,
                                chatType: 'private', // 默认私聊
                                chatName: '', // 私聊无群名
                                recordingMode: 'mcp_direct',
                                analysisReasoning: analysisData?.reasoning || 'No tools needed',
                                vcpFiltered: censorVcpOutput || (typeof vcpContentMaxLength === 'number' && vcpContentMaxLength > 0),
                                imageUrl: responseJson.image_url || null
                            }
                        );

                    } catch (logError) {
                        logger.error('对话记录', `MCP直接对话记录失败: ${logError.message}`);
                    }
                }

                res.json(responseJson);
            }
            return;
        }
        
        const requiredTools = analysisData.required_tools || [];
        logger.info('MCP工具', `needs_tools: ${analysisData.needs_tools}, required_tools: ${JSON.stringify(requiredTools)}`);

        // 统计工具执行次数
        const toolCounts = {};
        requiredTools.forEach(tool => {
            toolCounts[tool] = (toolCounts[tool] || 0) + 1;
        });
        const toolSummary = Object.entries(toolCounts).map(([tool, count]) =>
            count > 1 ? `${tool}(${count}次)` : tool
        ).join(', ');

        logger.info('MCP工具', `分析完成，需要依次执行 ${requiredTools.length} 个工具: ${toolSummary}`);
        logger.info('MCP工具', `分析推理: ${analysisData.reasoning}`);

        // === 新增：将工具分析结果添加到system消息中 ===
        const toolAnalysisInfo = formatToolAnalysisForSystem(analysisData, toolSummary, requiredTools.length, contextInfo);
        let currentMessages = [...processedBody.messages];

        // 查找并更新system消息，添加工具分析信息
        let hasSystemMessage = false;
        currentMessages = currentMessages.map(msg => {
            if (msg.role === 'system') {
                hasSystemMessage = true;
                return {
                    ...msg,
                    content: msg.content + toolAnalysisInfo
                };
            }
            return msg;
        });

        // 如果没有system消息，创建一个新的
        if (!hasSystemMessage) {
            currentMessages.unshift({
                role: 'system',
                content: `你是一个智能助手。${toolAnalysisInfo}`
            });
        }

        logger.info('MCP工具', `已将工具分析结果添加到system消息中，长度: ${toolAnalysisInfo.length} 字符`);

        // 第二步：依次强制执行每个工具
        
        // 调试：检查currentMessages初始化时是否包含上下文
        const systemMsgInCurrent = currentMessages.find(msg => msg.role === 'system');
        if (systemMsgInCurrent) {
            logger.debug('上下文调试', `currentMessages初始化 System消息内容 (前200字符): ${systemMsgInCurrent.content.substring(0, 200)}...`);
            if (systemMsgInCurrent.content.includes('=== 相关记忆和历史对话 ===')) {
                logger.success('上下文调试', 'currentMessages初始化时包含智能上下文!');
            } else {
                logger.warning('上下文调试', 'currentMessages初始化时不包含智能上下文');
            }
        } else {
            logger.warning('上下文调试', 'currentMessages初始化时没有system消息');
        }
        
        for (let i = 0; i < requiredTools.length; i++) {
            const toolName = requiredTools[i];

            // 计算这是该工具的第几次执行
            const toolExecutionCount = requiredTools.slice(0, i + 1).filter(t => t === toolName).length;
            const totalToolCount = requiredTools.filter(t => t === toolName).length;
            const executionInfo = totalToolCount > 1 ? ` (第${toolExecutionCount}/${totalToolCount}次)` : '';

            logger.info('MCP工具', `步骤${i + 2}：强制执行工具 ${i + 1}/${requiredTools.length}: ${toolName}${executionInfo}`);
            
            // 获取对应的工具定义
            const targetTool = processedBody.tools.find(t => t.function.name === toolName);
            if (!targetTool) {
                logger.error('MCP工具', `工具 ${toolName} 定义未找到`);
                continue;
            }
            
            // 构建强制调用该工具的请求
            const toolRequest = {
                model: OPENAI_TOOLS_MODEL,
                messages: currentMessages,
                tools: [targetTool],
                tool_choice: {
                    type: 'function',
                    function: { name: toolName }
                },
                temperature: 0.1
            };
            
            // 调用OpenAI强制执行该工具
            const toolResponse = await fetch(`${OPENAI_TOOLS_URL}/v1/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${OPENAI_TOOLS_KEY}`
                },
                body: JSON.stringify(toolRequest)
            });
            
            if (!toolResponse.ok) {
                const errorText = await toolResponse.text();
                logger.error('MCP工具', `工具 ${toolName} 调用失败: ${toolResponse.status} ${toolResponse.statusText} - ${errorText}`);
                continue;
            }
            
            const toolResult = await toolResponse.json();
            const toolMessage = toolResult.choices[0].message;
            
            if (toolMessage.tool_calls && toolMessage.tool_calls.length > 0) {
                const toolCall = toolMessage.tool_calls[0];
                const toolArgs = JSON.parse(toolCall.function.arguments);
                
                logger.info('MCP工具', `获得工具调用参数: ${JSON.stringify(toolArgs)}`);
                
                // 转换为VCP格式的工具调用
                let vcpToolCode = `<<<[TOOL_REQUEST]>>>\n`;
                vcpToolCode += `tool_name:「始」${toolName}「末」,\n`;
                
                for (const [key, value] of Object.entries(toolArgs)) {
                    vcpToolCode += `${key}:「始」${value}「末」,\n`;
                }
                vcpToolCode += `<<<[END_TOOL_REQUEST]>>>`;
                
                // 添加工具调用到上下文（作为assistant消息）
                currentMessages.push({
                    role: 'assistant',
                    content: vcpToolCode
                });
                
                logger.info('MCP工具', `执行VCP插件: ${toolName}`);
                
                try {
                    // 执行VCP插件（传递JSON字符串）
                    const pluginResult = await pluginManager.executePlugin(toolName, JSON.stringify(toolArgs));
                    
                    // 构建工具执行结果消息（作为user消息）
                    const resultMessage = `工具 ${toolName} 执行完成。执行结果：${JSON.stringify(pluginResult)}`;
                    
                    currentMessages.push({
                        role: 'user',
                        content: resultMessage
                    });
                    
                    logger.success('MCP工具', `工具 ${toolName} 执行成功，结果已添加到上下文`);
                    
                } catch (error) {
                    logger.error('MCP工具', `工具 ${toolName} 执行失败: ${error.message}`);
                    
                    // 添加错误结果到上下文
                    const errorMessage = `工具 ${toolName} 执行失败。错误信息：${error.message}`;
                    
                    currentMessages.push({
                        role: 'user',
                        content: errorMessage
                    });
                }
            }
        }
        
        // 第三步：使用用户指定的模型和API生成最终综合反馈
        logger.info('MCP工具', '步骤最终：使用用户模型生成综合反馈');
        
        const messages = (() => {
            const lastMsg = currentMessages[currentMessages.length - 1];

            // 如果最后消息是用户消息，则合并内容
            if (lastMsg?.role === 'user') {
                return [
                    ...currentMessages.slice(0, -1),
                    {
                        ...lastMsg,
                        content: `${lastMsg.content}\n\n根据以上所有工具的执行结果，请为用户提供一个综合性的最终回答。`
                    }
                ];
            }

            // 否则添加新消息
            return [
                ...currentMessages,
                {
                    role: 'user',
                    content: '根据以上所有工具的执行结果，请为用户提供一个综合性的最终回答。'
                }
            ];
        })();
        
        // 调试：最终检查messages数组中的上下文
        const finalSystemMsg = messages.find(msg => msg.role === 'system');
        if (finalSystemMsg) {
            logger.debug('上下文调试', `最终messages System消息内容 (前200字符): ${finalSystemMsg.content.substring(0, 200)}...`);
            if (finalSystemMsg.content.includes('=== 相关记忆和历史对话 ===')) {
                logger.success('上下文调试', '最终messages包含智能上下文!');
            } else {
                logger.warning('上下文调试', '最终messages不包含智能上下文');
            }
        } else {
            logger.error('上下文调试', '最终messages没有system消息！！！');
        }

        const finalRequest = {
            model: processedBody.model, // 使用用户请求的模型
            messages: messages,
            temperature: processedBody.temperature || 0.7,
            // 保留用户的其他参数
            ...(processedBody.top_p && { top_p: processedBody.top_p }),
            ...(processedBody.frequency_penalty && { frequency_penalty: processedBody.frequency_penalty }),
            ...(processedBody.presence_penalty && { presence_penalty: processedBody.presence_penalty }),
            ...(processedBody.stream && { stream: processedBody.stream })
        };
        
        // 调试：检查finalRequest中的system消息
        const systemMsgInFinal = finalRequest.messages.find(msg => msg.role === 'system');
        if (systemMsgInFinal) {
            logger.debug('上下文调试', `finalRequest System消息内容 (前200字符): ${systemMsgInFinal.content.substring(0, 200)}...`);

            // 完整打印system消息用于调试
            //console.log('\n' + '='.repeat(80));
            //console.log('🔍 完整的System消息内容:');
            //console.log('='.repeat(80));
            //console.log(systemMsgInFinal.content);
            //console.log('='.repeat(80));
            //console.log(`📏 System消息长度: ${systemMsgInFinal.content.length} 字符\n`);
        } else {
            logger.debug('上下文调试', 'finalRequest中没有system消息');
        }

        // 调试：确认finalRequest不包含tools参数
        if (finalRequest.tools) {
            logger.warning('MCP工具', '⚠️ finalRequest意外包含tools参数，这可能导致问题');
        } else {
            logger.success('MCP工具', '✅ finalRequest正确排除了tools参数');
        }

        //console.log(finalRequest)
        logger.info('MCP工具', `使用模型 ${finalRequest.model} 在 ${apiUrl} 生成最终回答`);
        
        const finalResponse = await fetch(`${apiUrl}/v1/chat/completions`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`,
                ...(req.headers['user-agent'] && { 'User-Agent': req.headers['user-agent'] }),
                'Accept': finalRequest.stream ? 'text/event-stream' : (req.headers['accept'] || 'application/json'),
            },
            body: JSON.stringify(finalRequest)
        });
        
        if (!finalResponse.ok) {
            const errorText = await finalResponse.text();
            throw new Error(`生成最终反馈失败: ${finalResponse.status} ${finalResponse.statusText} - ${errorText}`);
        }
        
        const isStreamingResponse = finalRequest.stream === true && finalResponse.headers.get('content-type')?.includes('text/event-stream');
        
        // 处理流式响应
            if (isStreamingResponse) {
            if (!res.headersSent) {
                res.setHeader('Content-Type', 'text/event-stream');
                res.setHeader('Cache-Control', 'no-cache');
                res.setHeader('Connection', 'keep-alive');
            }
            
            // MCP模式流式响应过滤处理
            if (censorVcpOutput || (typeof vcpContentMaxLength === 'number' && vcpContentMaxLength > 0)) {
                let collectedContent = '';
                
                finalResponse.body.on('data', (chunk) => {
                    const chunkString = chunk.toString('utf-8');
                    
                    // 收集内容用于过滤
                    const lines = chunkString.split('\n');
                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const jsonData = line.substring(5).trim();
                            if (jsonData !== '[DONE]' && jsonData) {
                                try {
                                    const parsedData = JSON.parse(jsonData);
                                    collectedContent += parsedData.choices?.[0]?.delta?.content || '';
                                } catch (e) { /* ignore */ }
                            }
                        }
                    }
                });
                
                finalResponse.body.on('end', () => {
                    // 过滤收集到的内容
                    const filteredContent = applyVcpFiltering(collectedContent, {
                        censor_vcp_output: censorVcpOutput,
                        vcp_content_max_length: vcpContentMaxLength
                    });
                    
                    if (DEBUG_MODE) {
                        logger.info('MCP过滤', `工具调用流式内容已过滤，原长度: ${collectedContent.length}, 过滤后长度: ${filteredContent.length}`);
                    }
                    
                    // 重新构建过滤后的响应
                    if (!res.writableEnded) {
                        const responseData = {
                            id: `chatcmpl-MCP-tools-filtered-${Date.now()}`,
                            object: "chat.completion",
                            created: Math.floor(Date.now() / 1000),
                            model: cleanedBody.model || "unknown",
                            choices: [{
                                index: 0,
                                message: {
                                    role: "assistant",
                                    content: filteredContent
                                },
                                finish_reason: "stop"
                            }]
                        };
                        
                        res.json(responseData);
                    }
                });
            } else {
            finalResponse.body.pipe(res);
            }
        } else {
            // 非流式响应
            const responseBuffer = await finalResponse.arrayBuffer();
            const responseText = Buffer.from(responseBuffer).toString('utf-8');
            let responseJson = JSON.parse(responseText);

            // 应用VCP过滤（MCP工具调用）
            let filteredToolContent = responseJson.choices[0].message.content;
            if (censorVcpOutput || (typeof vcpContentMaxLength === 'number' && vcpContentMaxLength > 0)) {
                filteredToolContent = applyVcpFiltering(filteredToolContent, {
                    censor_vcp_output: censorVcpOutput,
                    vcp_content_max_length: vcpContentMaxLength
                });
                responseJson.choices[0].message.content = filteredToolContent;
            }

            // 处理图片渲染
            if (originalBody.render_as_image === true) {
                try {
                    const HtmlToImageRenderer = require('./Puppeteer/HtmlToImageRenderer');
                    const renderer = new HtmlToImageRenderer();
                    
                    // 准备渲染数据
                    const renderData = {
                        model: cleanedBody.model || 'AI助手',
                        id1: originalBody.user_id || originalBody.userId || '10000',
                        name: originalBody.user_name || originalBody.userId || '用户',
                        MSG: originalUserMessageForRender || '', // 使用保存的原始消息
                        id2: originalBody.assistant_id || '10001',
                        name1: originalBody.assistant_name || originalBody.assistantName || 'AI助手',
                        CONTENT: filteredToolContent, // 使用过滤后的内容
                        theme: originalBody.theme || 'dark'
                    };

                    // 渲染图片
                    const imageUrl = await renderer.renderToImage(renderData);
                    
                    // 添加图片URL到响应
                    responseJson.image_url = `http://${req.headers.host}${imageUrl}`;
                    
                    // 关闭浏览器实例
                    await renderer.close();
                    
                    logger.info('图片渲染', `成功渲染对话图片: ${imageUrl}`);
                } catch (renderError) {
                    logger.error('图片渲染', `渲染对话图片失败: ${renderError.message}`);
                    responseJson.image_error = renderError.message;
                }
            }

            // === 新增：MCP日记处理功能（工具调用） ===
            await handleDiaryFromAIResponse(responseText);

            // === 新增：MCP工具调用对话记录功能 ===
            if (memoryTracking) {
                try {
                    // === 调用智能情感记忆系统记录 ===
                    await recordEmotionMemory(
                        userId,
                        originalUserContent,
                        filteredToolContent,
                        originalBody.assistantName || originalBody.assistant_name || 'Assistant',
                        {
                            type: 'mcp_tools',
                            model: cleanedBody.model || 'unknown',
                            ip: req.ip || req.connection.remoteAddress || 'unknown',
                            stream: false,
                            toolCalls: true,
                            toolsUsed: requiredTools,
                            chatType: 'private', // 默认私聊
                            chatName: '', // 私聊无群名
                            recordingMode: 'mcp_tools',
                            analysisReasoning: analysisData?.reasoning || 'Tools execution completed',
                            vcpFiltered: censorVcpOutput || (typeof vcpContentMaxLength === 'number' && vcpContentMaxLength > 0),
                            imageUrl: responseJson.image_url || null
                        }
                    );

                } catch (logError) {
                    logger.error('对话记录', `MCP工具调用对话记录失败: ${logError.message}`);
                }
            }

            res.json(responseJson);
        }
        
        logger.success('MCP工具', 'MCP模式请求处理完成');
        
    } catch (error) {
        logger.error('MCP工具', `MCP模式处理失败: ${error.message}`);
        
        if (!res.headersSent) {
            res.status(500).json({
                error: 'MCP_PROCESSING_ERROR',
                message: error.message,
                details: 'MCP模式处理失败'
            });
        }
    }
}