const WorldTreeManager = require('./WorldTreeManager');
const WorldTreeAPI = require('./WorldTreeAPI');

/**
 * 服务器集成模块
 * 负责将世界树系统集成到主服务器中
 */
class ServerIntegration {
    constructor() {
        this.manager = null;
        this.api = null;
        this.logger = null;
        this.isInitialized = false;
    }

    /**
     * 初始化集成
     */
    async initialize(config, logger, app) {
        this.logger = logger;
        
        try {
            // 初始化世界树管理器
            this.manager = new WorldTreeManager();
            await this.manager.initialize(config, logger);
            
            // 初始化API路由
            this.api = new WorldTreeAPI(this.manager, logger);
            
            // 注册API路由到Express应用
            if (app) {
                app.use('/admin_api/worldtree', this.api.getRouter());
                logger.info('世界树集成', '✅ API路由已注册到 /admin_api/worldtree');
            }
            
            this.isInitialized = true;
            logger.success('世界树集成', '✅ 服务器集成初始化完成');
            
        } catch (error) {
            logger.error('世界树集成', '初始化失败:', error);
            throw error;
        }
    }

    /**
     * 处理消息增强 - 主要集成点
     * 这个方法会被server.js在处理消息时调用
     */
    async enhanceMessage(originalBody, cleanedBody) {
        if (!this.isInitialized || !this.manager) {
            return;
        }

        try {
            await this.manager.enhanceSystemMessage(originalBody, cleanedBody);
        } catch (error) {
            this.logger.error('世界树集成', '消息增强失败:', error);
        }
    }

    /**
     * 检查是否应该为指定角色提供增强
     */
    shouldEnhanceAgent(agentName) {
        if (!this.isInitialized || !this.manager) {
            return false;
        }
        
        return this.manager.shouldEnhanceAgent(agentName);
    }

    /**
     * 获取管理器实例（用于其他模块访问）
     */
    getManager() {
        return this.manager;
    }

    /**
     * 获取API实例（用于其他模块访问）
     */
    getAPI() {
        return this.api;
    }

    /**
     * 销毁集成
     */
    async destroy() {
        if (this.manager) {
            await this.manager.destroy();
            this.manager = null;
        }
        
        this.api = null;
        this.isInitialized = false;
        
        if (this.logger) {
            this.logger.info('世界树集成', '服务器集成已销毁');
        }
    }
}

// 创建全局实例
const worldTreeIntegration = new ServerIntegration();

/**
 * 插件入口点 - 被Plugin.js调用
 */
async function initializePlugin(config, logger, app) {
    try {
        await worldTreeIntegration.initialize(config, logger, app);
        
        // 返回插件接口
        return {
            name: 'WorldTree',
            version: '1.0.0',
            
            // 消息处理钩子
            async processMessage(originalBody, cleanedBody) {
                await worldTreeIntegration.enhanceMessage(originalBody, cleanedBody);
            },
            
            // 检查是否应该处理指定角色
            shouldProcess(agentName) {
                return worldTreeIntegration.shouldEnhanceAgent(agentName);
            },
            
            // 获取管理器
            getManager() {
                return worldTreeIntegration.getManager();
            },
            
            // 获取API
            getAPI() {
                return worldTreeIntegration.getAPI();
            },
            
            // 销毁插件
            async destroy() {
                await worldTreeIntegration.destroy();
            }
        };
        
    } catch (error) {
        logger.error('世界树插件', '初始化失败:', error);
        throw error;
    }
}

/**
 * 插件销毁函数
 */
async function destroyPlugin() {
    await worldTreeIntegration.destroy();
}

module.exports = {
    initializePlugin,
    destroyPlugin,
    worldTreeIntegration
};
