const WorldTreeManager = require('./WorldTreeManager');
const WorldTreeAPI = require('./WorldTreeAPI');

// 全局变量
let manager = null;
let api = null;
let logger = null;
let isInitialized = false;

/**
 * 插件初始化函数
 */
async function initialize(config, loggerInstance, app, basePath) {
    logger = loggerInstance;

    try {
        logger.info('世界树插件', '开始初始化世界树角色管理系统...');

        // 初始化世界树管理器
        manager = new WorldTreeManager();
        await manager.initialize(config, logger);

        // 初始化API路由
        api = new WorldTreeAPI(manager, logger);

        // 注册API路由到Express应用
        if (app) {
            app.use('/admin_api/worldtree', api.getRouter());
            logger.info('世界树插件', '✅ API路由已注册到 /admin_api/worldtree');
        }

        isInitialized = true;
        logger.success('世界树插件', '✅ 世界树插件初始化完成');

    } catch (error) {
        logger.error('世界树插件', '初始化失败:', error);
        throw error;
    }
}

/**
 * 注册路由 - 插件系统调用的主要入口点
 */
async function registerRoutes(app, config, basePath) {
    try {
        // 初始化插件（如果还没有初始化）
        if (!isInitialized) {
            // 创建logger（如果没有传入）
            if (!logger) {
                logger = {
                    info: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
                    error: (component, msg, data) => console.error(`[${component}] ${msg}`, data || ''),
                    warning: (component, msg, data) => console.warn(`[${component}] ${msg}`, data || ''),
                    debug: (component, msg, data) => console.log(`[${component}] ${msg}`, data || ''),
                    success: (component, msg, data) => console.log(`[${component}] ✅ ${msg}`, data || '')
                };
            }

            await initialize(config, logger, app, basePath);
        }

        // 注册API路由
        if (api && app) {
            app.use('/admin_api/worldtree', api.getRouter());
            logger.info('世界树插件', '✅ API路由已注册到 /admin_api/worldtree');
        }

    } catch (error) {
        if (logger) {
            logger.error('世界树插件', '注册路由失败:', error);
        } else {
            console.error('[世界树插件] 注册路由失败:', error);
        }
        throw error;
    }
}

/**
 * 处理消息增强 - 主要集成点
 * 这个方法会被server.js在处理消息时调用
 */
async function processMessage(originalBody, cleanedBody) {
    if (!isInitialized || !manager) {
        return;
    }

    try {
        await manager.enhanceSystemMessage(originalBody, cleanedBody);
    } catch (error) {
        if (logger) {
            logger.error('世界树插件', '消息增强失败:', error);
        }
    }
}

/**
 * 检查是否应该为指定角色提供增强
 */
function shouldProcess(agentName) {
    if (!isInitialized || !manager) {
        return false;
    }

    return manager.shouldEnhanceAgent(agentName);
}

/**
 * 获取管理器实例（用于其他模块访问）
 */
function getManager() {
    return manager;
}

/**
 * 获取API实例（用于其他模块访问）
 */
function getAPI() {
    return api;
}

/**
 * 插件关闭函数
 */
async function shutdown() {
    if (manager) {
        await manager.destroy();
        manager = null;
    }

    api = null;
    isInitialized = false;

    if (logger) {
        logger.info('世界树插件', '世界树插件已关闭');
    }
}

// 导出插件接口 - 符合插件系统要求
module.exports = {
    // 主要接口 - 插件系统会调用这个
    registerRoutes,

    // 其他接口
    initialize,
    processMessage,
    shouldProcess,
    getManager,
    getAPI,
    shutdown
};
