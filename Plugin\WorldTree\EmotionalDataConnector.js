const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const { promisify } = require('util');

/**
 * 情感数据连接器
 * 连接到AdvancedMemorySystem数据库，获取用户的情感、压力等数据
 */
class EmotionalDataConnector {
    constructor(logger) {
        this.logger = logger;
        this.memoryDb = null;
        this.dbGet = null;
        this.dbAll = null;
        this.isConnected = false;
    }

    /**
     * 连接到AdvancedMemorySystem数据库
     */
    async connect() {
        try {
            const memoryDbPath = path.join(__dirname, '../AdvancedMemorySystem/memory_system.db');
            
            return new Promise((resolve, reject) => {
                this.memoryDb = new sqlite3.Database(memoryDbPath, sqlite3.OPEN_READONLY, (err) => {
                    if (err) {
                        this.logger.warning('情感数据连接器', '无法连接到AdvancedMemorySystem数据库:', err.message);
                        resolve(false); // 不抛出错误，允许系统继续运行
                        return;
                    }
                    
                    this.dbGet = promisify(this.memoryDb.get.bind(this.memoryDb));
                    this.dbAll = promisify(this.memoryDb.all.bind(this.memoryDb));
                    this.isConnected = true;
                    
                    this.logger.info('情感数据连接器', '✅ 已连接到AdvancedMemorySystem数据库');
                    resolve(true);
                });
            });
            
        } catch (error) {
            this.logger.warning('情感数据连接器', '连接数据库失败:', error.message);
            return false;
        }
    }

    /**
     * 获取用户的情感记忆数据
     */
    async getEmotionalData(userId, personaName) {
        if (!this.isConnected) {
            return this.getDefaultEmotionalData();
        }

        try {
            const data = {
                affinity: await this.getUserAffinity(userId, personaName),
                stress: await this.getStressState(userId, personaName),
                emotions: await this.getEmotionState(userId, personaName),
                recentInteractions: await this.getRecentInteractions(userId, personaName),
                memoryFragments: await this.getRecentMemoryFragments(userId, personaName)
            };

            return data;

        } catch (error) {
            this.logger.error('情感数据连接器', '获取情感数据失败:', error);
            return this.getDefaultEmotionalData();
        }
    }

    /**
     * 获取用户好感度数据
     */
    async getUserAffinity(userId, personaName) {
        try {
            const result = await this.dbGet(`
                SELECT * FROM user_affinity 
                WHERE user_id = ? AND persona_name = ?
            `, [userId, personaName]);

            if (result) {
                return {
                    current_affinity: result.current_affinity || 0.0,
                    relationship_type: result.relationship_type || 'stranger',
                    emotion_valence: result.emotion_valence || 0.0,
                    emotion_arousal: result.emotion_arousal || 0.0,
                    emotion_dominance: result.emotion_dominance || 0.0,
                    total_interactions: result.total_interactions || 0,
                    positive_interactions: result.positive_interactions || 0,
                    negative_interactions: result.negative_interactions || 0,
                    last_interaction: result.last_interaction
                };
            }

            return null;

        } catch (error) {
            this.logger.error('情感数据连接器', '获取好感度数据失败:', error);
            return null;
        }
    }

    /**
     * 获取压力状态数据
     */
    async getStressState(userId, personaName) {
        try {
            const result = await this.dbGet(`
                SELECT * FROM ai_stress_states 
                WHERE user_id = ? AND persona_name = ?
            `, [userId, personaName]);

            if (result) {
                return {
                    stress_value: result.stress_value || 0.0,
                    stress_level: result.stress_level || 'normal',
                    stress_factors: result.stress_factors ? JSON.parse(result.stress_factors) : {},
                    behavior_impact: result.behavior_impact ? JSON.parse(result.behavior_impact) : {},
                    trend: result.trend || 'stable',
                    timestamp: result.timestamp
                };
            }

            return null;

        } catch (error) {
            this.logger.error('情感数据连接器', '获取压力状态失败:', error);
            return null;
        }
    }

    /**
     * 获取情绪状态数据（如果存在user_emotions表）
     */
    async getEmotionState(userId, personaName) {
        try {
            // 检查是否存在user_emotions表
            const tableExists = await this.dbGet(`
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='user_emotions'
            `);

            if (!tableExists) {
                return null;
            }

            const result = await this.dbGet(`
                SELECT * FROM user_emotions 
                WHERE user_id = ? AND persona_name = ?
            `, [userId, personaName]);

            if (result) {
                return {
                    emotion_value: result.emotion_value || 0.0,
                    current_emotion: result.current_emotion || 'neutral',
                    confidence_score: result.confidence_score || 0.5,
                    last_update: result.last_update
                };
            }

            return null;

        } catch (error) {
            this.logger.error('情感数据连接器', '获取情绪状态失败:', error);
            return null;
        }
    }

    /**
     * 获取最近的交互记录
     */
    async getRecentInteractions(userId, personaName, limit = 5) {
        try {
            const results = await this.dbAll(`
                SELECT * FROM conversation_history 
                WHERE user_id = ? AND persona_name = ?
                ORDER BY timestamp DESC 
                LIMIT ?
            `, [userId, personaName, limit]);

            return results || [];

        } catch (error) {
            this.logger.error('情感数据连接器', '获取最近交互失败:', error);
            return [];
        }
    }

    /**
     * 获取最近的记忆片段
     */
    async getRecentMemoryFragments(userId, personaName, limit = 3) {
        try {
            const results = await this.dbAll(`
                SELECT * FROM memory_fragments 
                WHERE user_id = ? AND persona_name = ?
                ORDER BY creation_time DESC 
                LIMIT ?
            `, [userId, personaName, limit]);

            return results || [];

        } catch (error) {
            this.logger.error('情感数据连接器', '获取记忆片段失败:', error);
            return [];
        }
    }

    /**
     * 获取模因认知状态
     */
    async getMemeticState(userId, personaName) {
        try {
            const result = await this.dbGet(`
                SELECT * FROM meme_cognition_states 
                WHERE user_id = ? AND persona_name = ?
            `, [userId, personaName]);

            if (result) {
                return {
                    active_memes: result.active_memes ? JSON.parse(result.active_memes) : [],
                    meme_network: result.meme_network ? JSON.parse(result.meme_network) : {},
                    cognitive_patterns: result.cognitive_patterns ? JSON.parse(result.cognitive_patterns) : {},
                    memetic_influence: result.memetic_influence || 0.0,
                    evolution_stage: result.evolution_stage || 'initial',
                    timestamp: result.timestamp
                };
            }

            return null;

        } catch (error) {
            this.logger.error('情感数据连接器', '获取模因状态失败:', error);
            return null;
        }
    }

    /**
     * 获取世界树背景状态
     */
    async getWorldTreeState(userId, personaName) {
        try {
            const result = await this.dbGet(`
                SELECT * FROM world_tree_states 
                WHERE user_id = ? AND persona_name = ?
            `, [userId, personaName]);

            if (result) {
                return {
                    current_branch: result.current_branch,
                    narrative_context: result.narrative_context ? JSON.parse(result.narrative_context) : {},
                    world_state: result.world_state ? JSON.parse(result.world_state) : {},
                    character_role: result.character_role,
                    story_progression: result.story_progression || 0.0,
                    background_influence: result.background_influence ? JSON.parse(result.background_influence) : {},
                    timestamp: result.timestamp
                };
            }

            return null;

        } catch (error) {
            this.logger.error('情感数据连接器', '获取世界树状态失败:', error);
            return null;
        }
    }

    /**
     * 获取默认情感数据（当数据库不可用时）
     */
    getDefaultEmotionalData() {
        return {
            affinity: {
                current_affinity: 0.5,
                relationship_type: 'neutral',
                emotion_valence: 0.0,
                emotion_arousal: 0.0,
                emotion_dominance: 0.0,
                total_interactions: 0,
                positive_interactions: 0,
                negative_interactions: 0
            },
            stress: {
                stress_value: 0.0,
                stress_level: 'normal',
                stress_factors: {},
                behavior_impact: {},
                trend: 'stable'
            },
            emotions: {
                emotion_value: 0.0,
                current_emotion: 'neutral',
                confidence_score: 0.5
            },
            recentInteractions: [],
            memoryFragments: []
        };
    }

    /**
     * 分析情感数据并生成心理状态描述
     */
    analyzeEmotionalState(emotionalData) {
        const analysis = {
            overallMood: 'neutral',
            stressLevel: 'normal',
            socialConnection: 'neutral',
            cognitiveState: 'stable',
            recommendations: []
        };

        // 分析好感度
        if (emotionalData.affinity) {
            const affinity = emotionalData.affinity.current_affinity;
            if (affinity > 0.7) {
                analysis.socialConnection = 'positive';
            } else if (affinity < 0.3) {
                analysis.socialConnection = 'negative';
            }
        }

        // 分析压力状态
        if (emotionalData.stress) {
            analysis.stressLevel = emotionalData.stress.stress_level;
            if (emotionalData.stress.stress_value > 0.6) {
                analysis.recommendations.push('需要放松和减压');
            }
        }

        // 分析情绪状态
        if (emotionalData.emotions) {
            analysis.overallMood = emotionalData.emotions.current_emotion;
        }

        // 分析交互历史
        if (emotionalData.recentInteractions && emotionalData.recentInteractions.length > 0) {
            const recentCount = emotionalData.recentInteractions.length;
            if (recentCount > 3) {
                analysis.recommendations.push('最近交互频繁，注意情感维护');
            }
        }

        return analysis;
    }

    /**
     * 断开数据库连接
     */
    async disconnect() {
        if (this.memoryDb) {
            this.memoryDb.close();
            this.memoryDb = null;
            this.isConnected = false;
            this.logger.info('情感数据连接器', '已断开AdvancedMemorySystem数据库连接');
        }
    }
}

module.exports = EmotionalDataConnector;
