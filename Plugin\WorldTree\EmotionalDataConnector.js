const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const { promisify } = require('util');

/**
 * 情感数据连接器
 * 连接到AdvancedMemorySystem数据库，获取用户的情感、压力等数据
 */
class EmotionalDataConnector {
    constructor(logger) {
        this.logger = logger;
        this.memoryDb = null;
        this.dbGet = null;
        this.dbAll = null;
        this.isConnected = false;
    }

    /**
     * 连接到AdvancedMemorySystem数据库
     */
    async connect() {
        try {
            // 尝试多个可能的数据库路径
            const possiblePaths = [
                path.join(__dirname, '../AdvancedMemorySystem/data/emotion_memory.db'),
                path.join(__dirname, '../AdvancedMemorySystem/memory_system.db'),
                path.join(__dirname, '../../data/emotion_memory.db'),
                path.join(__dirname, '../../Plugin/AdvancedMemorySystem/data/emotion_memory.db')
            ];

            for (const dbPath of possiblePaths) {
                try {
                    const fs = require('fs');
                    if (fs.existsSync(dbPath)) {
                        this.logger.info('情感数据连接器', `找到数据库文件: ${dbPath}`);

                        return new Promise((resolve, reject) => {
                            this.memoryDb = new sqlite3.Database(dbPath, sqlite3.OPEN_READONLY, (err) => {
                                if (err) {
                                    this.logger.warning('情感数据连接器', `连接数据库失败 ${dbPath}:`, err.message);
                                    resolve(false);
                                    return;
                                }

                                this.dbGet = promisify(this.memoryDb.get.bind(this.memoryDb));
                                this.dbAll = promisify(this.memoryDb.all.bind(this.memoryDb));
                                this.isConnected = true;

                                this.logger.info('情感数据连接器', `✅ 已连接到AdvancedMemorySystem数据库: ${dbPath}`);
                                resolve(true);
                            });
                        });
                    }
                } catch (pathError) {
                    this.logger.debug('情感数据连接器', `检查路径失败 ${dbPath}:`, pathError.message);
                }
            }

            this.logger.warning('情感数据连接器', '未找到AdvancedMemorySystem数据库文件，将使用默认数据');
            return false;

        } catch (error) {
            this.logger.warning('情感数据连接器', '连接数据库失败:', error.message);
            return false;
        }
    }

    /**
     * 获取用户的情感记忆数据
     */
    async getEmotionalData(userId, personaName) {
        if (!this.isConnected) {
            this.logger.info('情感数据连接器', '数据库未连接，使用默认数据');
            return this.getDefaultEmotionalData();
        }

        try {
            this.logger.debug('情感数据连接器', `获取用户 ${userId} 和角色 ${personaName} 的情感数据`);

            const data = {
                affinity: await this.getUserAffinity(userId, personaName),
                stress: await this.getStressState(userId, personaName),
                emotions: await this.getEmotionState(userId, personaName),
                recentInteractions: await this.getRecentInteractions(userId, personaName),
                memoryFragments: await this.getRecentMemoryFragments(userId, personaName),
                memeticState: await this.getMemeticState(userId, personaName),
                worldTreeState: await this.getWorldTreeState(userId, personaName)
            };

            // 记录获取到的数据统计
            const stats = {
                hasAffinity: !!data.affinity,
                hasStress: !!data.stress,
                hasEmotions: !!data.emotions,
                interactionCount: data.recentInteractions ? data.recentInteractions.length : 0,
                memoryCount: data.memoryFragments ? data.memoryFragments.length : 0,
                hasMemeticState: !!data.memeticState,
                hasWorldTreeState: !!data.worldTreeState
            };

            this.logger.info('情感数据连接器', `获取情感数据完成: ${JSON.stringify(stats)}`);

            return data;

        } catch (error) {
            this.logger.error('情感数据连接器', '获取情感数据失败:', error);
            return this.getDefaultEmotionalData();
        }
    }

    /**
     * 获取用户好感度数据
     */
    async getUserAffinity(userId, personaName) {
        try {
            const result = await this.dbGet(`
                SELECT * FROM user_affinity
                WHERE user_id = ? AND persona_name = ?
            `, [userId, personaName]);

            if (result) {
                let affinityHistory = [];

                // 安全解析affinity_history JSON字段
                try {
                    affinityHistory = result.affinity_history ? JSON.parse(result.affinity_history) : [];
                } catch (e) {
                    this.logger.warn('情感数据连接器', '解析affinity_history失败:', e.message);
                }

                return {
                    current_affinity: result.current_affinity || 0.0,
                    relationship_type: result.relationship_type || 'stranger',
                    emotion_valence: result.emotion_valence || 0.0,
                    emotion_arousal: result.emotion_arousal || 0.0,
                    emotion_dominance: result.emotion_dominance || 0.0,
                    total_interactions: result.total_interactions || 0,
                    positive_interactions: result.positive_interactions || 0,
                    negative_interactions: result.negative_interactions || 0,
                    last_interaction: result.last_interaction,
                    affinity_history: affinityHistory
                };
            }

            return null;

        } catch (error) {
            this.logger.error('情感数据连接器', '获取好感度数据失败:', error);
            return null;
        }
    }

    /**
     * 获取压力状态数据
     */
    async getStressState(userId, personaName) {
        try {
            const result = await this.dbGet(`
                SELECT * FROM ai_stress_states
                WHERE user_id = ? AND persona_name = ?
            `, [userId, personaName]);

            if (result) {
                let stressFactors = {};
                let behaviorImpact = {};

                // 安全解析JSON字段
                try {
                    stressFactors = result.stress_factors ? JSON.parse(result.stress_factors) : {};
                } catch (e) {
                    this.logger.warn('情感数据连接器', '解析stress_factors失败:', e.message);
                }

                try {
                    behaviorImpact = result.behavior_impact ? JSON.parse(result.behavior_impact) : {};
                } catch (e) {
                    this.logger.warn('情感数据连接器', '解析behavior_impact失败:', e.message);
                }

                return {
                    stress_value: result.stress_value || 0.0,
                    stress_level: result.stress_level || 'normal',
                    stress_factors: stressFactors,
                    behavior_impact: behaviorImpact,
                    trend: result.trend || 'stable',
                    timestamp: result.timestamp
                };
            }

            return null;

        } catch (error) {
            this.logger.error('情感数据连接器', '获取压力状态失败:', error);
            return null;
        }
    }

    /**
     * 获取情绪状态数据（如果存在user_emotions表）
     */
    async getEmotionState(userId, personaName) {
        try {
            // 检查是否存在user_emotions表
            const tableExists = await this.dbGet(`
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name='user_emotions'
            `);

            if (!tableExists) {
                return null;
            }

            const result = await this.dbGet(`
                SELECT * FROM user_emotions 
                WHERE user_id = ? AND persona_name = ?
            `, [userId, personaName]);

            if (result) {
                return {
                    emotion_value: result.emotion_value || 0.0,
                    current_emotion: result.current_emotion || 'neutral',
                    confidence_score: result.confidence_score || 0.5,
                    last_update: result.last_update
                };
            }

            return null;

        } catch (error) {
            this.logger.error('情感数据连接器', '获取情绪状态失败:', error);
            return null;
        }
    }

    /**
     * 获取最近的交互记录
     */
    async getRecentInteractions(userId, personaName, limit = 5) {
        try {
            // 首先尝试从conversation_sessions表获取会话记录
            let results = await this.dbAll(`
                SELECT * FROM conversation_sessions
                WHERE user_id = ? AND persona_name = ?
                ORDER BY last_update_time DESC
                LIMIT ?
            `, [userId, personaName, limit]);

            // 如果没有会话记录，尝试从conversation_history表获取
            if (!results || results.length === 0) {
                results = await this.dbAll(`
                    SELECT * FROM conversation_history
                    WHERE user_id = ? AND persona_name = ?
                    ORDER BY timestamp DESC
                    LIMIT ?
                `, [userId, personaName, limit]);
            }

            return results || [];

        } catch (error) {
            this.logger.error('情感数据连接器', '获取最近交互失败:', error);
            return [];
        }
    }

    /**
     * 获取最近的记忆片段
     */
    async getRecentMemoryFragments(userId, personaName, limit = 3) {
        try {
            const results = await this.dbAll(`
                SELECT * FROM memory_fragments 
                WHERE user_id = ? AND persona_name = ?
                ORDER BY creation_time DESC 
                LIMIT ?
            `, [userId, personaName, limit]);

            return results || [];

        } catch (error) {
            this.logger.error('情感数据连接器', '获取记忆片段失败:', error);
            return [];
        }
    }

    /**
     * 获取模因认知状态
     */
    async getMemeticState(userId, personaName) {
        try {
            const result = await this.dbGet(`
                SELECT * FROM meme_cognition_states
                WHERE user_id = ? AND persona_name = ?
            `, [userId, personaName]);

            if (result) {
                let activeMemes = [];
                let memeNetwork = {};
                let cognitivePatterns = {};

                // 安全解析JSON字段
                try {
                    activeMemes = result.active_memes ? JSON.parse(result.active_memes) : [];
                } catch (e) {
                    this.logger.warn('情感数据连接器', '解析active_memes失败:', e.message);
                }

                try {
                    memeNetwork = result.meme_network ? JSON.parse(result.meme_network) : {};
                } catch (e) {
                    this.logger.warn('情感数据连接器', '解析meme_network失败:', e.message);
                }

                try {
                    cognitivePatterns = result.cognitive_patterns ? JSON.parse(result.cognitive_patterns) : {};
                } catch (e) {
                    this.logger.warn('情感数据连接器', '解析cognitive_patterns失败:', e.message);
                }

                return {
                    active_memes: activeMemes,
                    meme_network: memeNetwork,
                    cognitive_patterns: cognitivePatterns,
                    memetic_influence: result.memetic_influence || 0.0,
                    evolution_stage: result.evolution_stage || 'initial',
                    timestamp: result.timestamp
                };
            }

            return null;

        } catch (error) {
            this.logger.error('情感数据连接器', '获取模因状态失败:', error);
            return null;
        }
    }

    /**
     * 获取世界树背景状态
     */
    async getWorldTreeState(userId, personaName) {
        try {
            const result = await this.dbGet(`
                SELECT * FROM world_tree_states
                WHERE user_id = ? AND persona_name = ?
            `, [userId, personaName]);

            if (result) {
                let narrativeContext = {};
                let worldState = {};
                let backgroundInfluence = {};

                // 安全解析JSON字段
                try {
                    narrativeContext = result.narrative_context ? JSON.parse(result.narrative_context) : {};
                } catch (e) {
                    this.logger.warn('情感数据连接器', '解析narrative_context失败:', e.message);
                }

                try {
                    worldState = result.world_state ? JSON.parse(result.world_state) : {};
                } catch (e) {
                    this.logger.warn('情感数据连接器', '解析world_state失败:', e.message);
                }

                try {
                    backgroundInfluence = result.background_influence ? JSON.parse(result.background_influence) : {};
                } catch (e) {
                    this.logger.warn('情感数据连接器', '解析background_influence失败:', e.message);
                }

                return {
                    current_branch: result.current_branch,
                    narrative_context: narrativeContext,
                    world_state: worldState,
                    character_role: result.character_role,
                    story_progression: result.story_progression || 0.0,
                    background_influence: backgroundInfluence,
                    timestamp: result.timestamp
                };
            }

            return null;

        } catch (error) {
            this.logger.error('情感数据连接器', '获取世界树状态失败:', error);
            return null;
        }
    }

    /**
     * 获取默认情感数据（当数据库不可用时）
     */
    getDefaultEmotionalData() {
        return {
            affinity: {
                current_affinity: 0.5,
                relationship_type: 'neutral',
                emotion_valence: 0.0,
                emotion_arousal: 0.0,
                emotion_dominance: 0.0,
                total_interactions: 0,
                positive_interactions: 0,
                negative_interactions: 0
            },
            stress: {
                stress_value: 0.0,
                stress_level: 'normal',
                stress_factors: {},
                behavior_impact: {},
                trend: 'stable'
            },
            emotions: {
                emotion_value: 0.0,
                current_emotion: 'neutral',
                confidence_score: 0.5
            },
            recentInteractions: [],
            memoryFragments: []
        };
    }

    /**
     * 分析情感数据并生成心理状态描述
     */
    analyzeEmotionalState(emotionalData) {
        const analysis = {
            overallMood: 'neutral',
            stressLevel: 'normal',
            socialConnection: 'neutral',
            cognitiveState: 'stable',
            recommendations: []
        };

        // 分析好感度
        if (emotionalData.affinity) {
            const affinity = emotionalData.affinity.current_affinity;
            if (affinity > 0.7) {
                analysis.socialConnection = 'positive';
            } else if (affinity < 0.3) {
                analysis.socialConnection = 'negative';
            }
        }

        // 分析压力状态
        if (emotionalData.stress) {
            analysis.stressLevel = emotionalData.stress.stress_level;
            if (emotionalData.stress.stress_value > 0.6) {
                analysis.recommendations.push('需要放松和减压');
            }
        }

        // 分析情绪状态
        if (emotionalData.emotions) {
            analysis.overallMood = emotionalData.emotions.current_emotion;
        }

        // 分析交互历史
        if (emotionalData.recentInteractions && emotionalData.recentInteractions.length > 0) {
            const recentCount = emotionalData.recentInteractions.length;
            if (recentCount > 3) {
                analysis.recommendations.push('最近交互频繁，注意情感维护');
            }
        }

        return analysis;
    }

    /**
     * 断开数据库连接
     */
    async disconnect() {
        if (this.memoryDb) {
            this.memoryDb.close();
            this.memoryDb = null;
            this.isConnected = false;
            this.logger.info('情感数据连接器', '已断开AdvancedMemorySystem数据库连接');
        }
    }
}

module.exports = EmotionalDataConnector;
