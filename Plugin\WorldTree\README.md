# 世界树角色管理系统 (WorldTree)

## 概述

世界树角色管理系统是一个高级的VCP插件，用于管理AI角色的时间架构、日程表和心理活动。该系统能够根据角色名称动态增强system消息，结合现有的情感记忆系统数据来构建bot的心理活动，显著提升AI角色的拟人性表现。

## 主要功能

### 1. 角色时间架构管理
- **时区设置**: 支持多时区配置
- **工作时间**: 定义角色的工作时间段
- **休息时间**: 设置休息和睡眠时间
- **活跃时间**: 配置角色的活跃时间段
- **精力模式**: 定义精力高峰和低谷时段

### 2. 日程表管理
- **事件创建**: 添加、编辑、删除日程事件
- **状态跟踪**: 自动更新事件状态（scheduled, active, completed, cancelled）
- **优先级管理**: 支持1-5级优先级设置
- **类型分类**: 支持工作、休息、社交等事件类型

### 3. 心理活动分析
- **情感数据集成**: 连接AdvancedMemorySystem数据库获取情感状态
- **AI心理分析**: 使用OpenAI API生成角色心理活动
- **上下文感知**: 基于对话上下文和历史数据分析
- **实时生成**: 动态生成符合角色状态的心理活动

### 4. 分层架构支持
- **多用户支持**: 一个角色可以与多个用户交互
- **关系管理**: 跟踪用户-角色关系等级和交互历史
- **状态快照**: 支持创建和恢复角色状态快照
- **会话管理**: 基于会话ID管理对话历史

## 系统架构

```
WorldTree/
├── WorldTreeCore.js          # 核心逻辑
├── WorldTreeManager.js       # 管理器接口
├── WorldTreeAPI.js          # REST API接口
├── ServerIntegration.js     # 服务器集成
├── EmotionalDataConnector.js # 情感数据连接器
├── TimeArchitectureManager.js # 时间架构管理器
├── plugin-manifest.json     # 插件清单
├── config.env              # 配置文件
└── README.md               # 说明文档
```

## 配置说明

### 环境变量配置 (config.env)

```env
# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# 功能开关
PSYCHOLOGY_ANALYSIS_ENABLED=true
DEBUG_MODE=false

# 更新间隔设置
SCHEDULE_UPDATE_INTERVAL=30
```

### 数据库结构

系统使用SQLite数据库存储以下数据：

1. **agent_configs**: 角色配置信息
2. **psychology_activities**: 心理活动记录
3. **schedule_events**: 日程事件
4. **user_agent_relationships**: 用户-角色关系
5. **agent_state_snapshots**: 状态快照

## API接口

### 角色管理
- `GET /admin_api/worldtree/agents` - 获取所有角色
- `GET /admin_api/worldtree/agents/:agentName` - 获取指定角色配置
- `PUT /admin_api/worldtree/agents/:agentName` - 更新角色配置

### 日程管理
- `GET /admin_api/worldtree/agents/:agentName/schedule` - 获取角色日程
- `POST /admin_api/worldtree/agents/:agentName/schedule` - 添加日程事件
- `PUT /admin_api/worldtree/schedule/:eventId` - 更新日程事件
- `DELETE /admin_api/worldtree/schedule/:eventId` - 删除日程事件

### 心理活动
- `GET /admin_api/worldtree/users/:userId/agents/:agentName/psychology` - 获取心理活动记录
- `POST /admin_api/worldtree/agents/:agentName/psychology/analyze` - 手动触发心理分析

### 用户关系
- `GET /admin_api/worldtree/users/:userId/relationships` - 获取用户角色关系
- `GET /admin_api/worldtree/agents/:agentName/users` - 获取角色用户列表

### 状态快照
- `POST /admin_api/worldtree/users/:userId/agents/:agentName/snapshots` - 创建状态快照
- `GET /admin_api/worldtree/users/:userId/agents/:agentName/snapshots` - 获取状态快照列表

## 使用方法

### 1. 插件安装
1. 将WorldTree文件夹放置在Plugin目录下
2. 配置config.env文件中的API密钥
3. 重启服务器以加载插件

### 2. 管理面板配置
1. 访问管理面板
2. 在侧边栏选择"世界树管理"
3. 选择要配置的角色
4. 设置时间架构、性格特征等

### 3. 系统集成
插件会自动集成到主系统中：
- 当使用指定角色名称时，自动注入世界树增强内容
- 结合情感记忆系统数据生成心理活动
- 根据时间架构调整角色状态

## 时间架构示例

```json
{
  "timezone": "Asia/Shanghai",
  "workingHours": {
    "start": "09:00",
    "end": "18:00"
  },
  "restHours": {
    "start": "22:00",
    "end": "07:00"
  },
  "energyPattern": {
    "peak": ["09:00", "14:00", "19:00"],
    "low": ["13:00", "15:00", "21:00"]
  }
}
```

## 心理活动生成

系统会基于以下因素生成心理活动：
- 当前时间和时间阶段
- 用户情感状态数据
- 角色压力和精力等级
- 对话上下文和历史
- 角色性格特征

## 注意事项

1. **API密钥**: 确保配置有效的OpenAI API密钥
2. **数据库权限**: 确保对AdvancedMemorySystem数据库有读取权限
3. **时间格式**: 所有时间使用本地时间格式 (YYYY-MM-DD HH:mm:ss)
4. **性能考虑**: 心理分析会调用外部API，注意频率限制

## 故障排除

### 常见问题

1. **插件未启用**
   - 检查plugin-manifest.json格式
   - 确认config.env配置正确
   - 查看服务器日志

2. **心理分析失败**
   - 验证OpenAI API密钥
   - 检查网络连接
   - 确认模型可用性

3. **情感数据获取失败**
   - 确认AdvancedMemorySystem插件已启用
   - 检查数据库文件权限
   - 验证数据库结构

## 开发说明

### 扩展功能
可以通过以下方式扩展功能：
1. 在WorldTreeCore.js中添加新的分析方法
2. 在WorldTreeAPI.js中添加新的API端点
3. 在前端添加新的管理界面

### 数据格式
所有JSON数据都使用UTF-8编码，时间格式统一使用本地时间字符串。

## 版本信息

- **版本**: 1.0.0
- **作者**: VCPSystem
- **兼容性**: VCPToolBox v2.0+
- **依赖**: AdvancedMemorySystem, OpenAI API

## 许可证

本插件遵循MIT许可证。
